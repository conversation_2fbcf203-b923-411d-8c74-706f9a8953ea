import React from 'react';
import { useChatbot } from '../contexts/ChatbotContext';
import { Clock, MessageSquare, Heart, TrendingUp } from 'lucide-react';

const ConversationInsights = () => {
  const { getConversationSummary, userContext } = useChatbot();
  const summary = getConversationSummary();

  if (summary.messageCount < 5) {
    return null; // Only show after meaningful conversation
  }

  return (
    <div className="conversation-insights">
      <div className="insights-header">
        <TrendingUp size={16} />
        <span>Votre session</span>
      </div>
      
      <div className="insights-grid">
        <div className="insight-item">
          <MessageSquare size={14} />
          <span>{summary.messageCount} messages</span>
        </div>
        
        <div className="insight-item">
          <Clock size={14} />
          <span>{summary.sessionDuration} min</span>
        </div>
        
        {summary.userInterests.length > 0 && (
          <div className="insight-item">
            <Heart size={14} />
            <span>{summary.userInterests.join(', ')}</span>
          </div>
        )}
      </div>

      <style>{`
        .conversation-insights {
          padding: 0.75rem 1rem;
          background: linear-gradient(135deg, rgba(0, 51, 102, 0.05) 0%, rgba(255, 215, 0, 0.05) 100%);
          border-top: 1px solid rgba(0, 51, 102, 0.1);
          font-size: 0.8rem;
        }

        .insights-header {
          display: flex;
          align-items: center;
          gap: 0.5rem;
          color: var(--primary-blue);
          font-weight: 600;
          margin-bottom: 0.5rem;
        }

        .insights-grid {
          display: flex;
          gap: 1rem;
          flex-wrap: wrap;
        }

        .insight-item {
          display: flex;
          align-items: center;
          gap: 0.25rem;
          color: var(--medium-gray);
          font-size: 0.75rem;
        }

        .insight-item svg {
          opacity: 0.7;
        }
      `}</style>
    </div>
  );
};

export default ConversationInsights;
