import React, { useEffect } from 'react';
import { useChatbot } from '../contexts/ChatbotContext';
import { useLanguage } from '../contexts/LanguageContext';

const ChatbotTest = () => {
  const { sendMessage, messages, isOpen, openChatbot } = useChatbot();
  const { t, changeLanguage } = useLanguage();

  const testMessages = [
    'Bonjour',
    'Je cherche des timbres',
    'Quels sont vos prix ?',
    'Comment passer commande ?',
    'Informations livraison',
    'Merci'
  ];

  const runTest = () => {
    if (!isOpen) {
      openChatbot();
    }
    
    testMessages.forEach((message, index) => {
      setTimeout(() => {
        sendMessage(message);
      }, index * 3000); // Send a message every 3 seconds
    });
  };

  const testLanguageSwitch = () => {
    changeLanguage('ar');
    setTimeout(() => changeLanguage('en'), 2000);
    setTimeout(() => changeLanguage('fr'), 4000);
  };

  return (
    <div style={{ 
      position: 'fixed', 
      top: '10px', 
      left: '10px', 
      zIndex: 9999, 
      background: 'white', 
      padding: '10px', 
      border: '1px solid #ccc',
      borderRadius: '5px',
      display: 'none' // Hidden by default - only for development testing
    }}>
      <h4>Chatbot Test Panel</h4>
      <button onClick={runTest} style={{ margin: '5px' }}>
        Test Conversation
      </button>
      <button onClick={testLanguageSwitch} style={{ margin: '5px' }}>
        Test Languages
      </button>
      <button onClick={openChatbot} style={{ margin: '5px' }}>
        Open Chatbot
      </button>
      <div>
        <small>Messages: {messages.length}</small>
      </div>
    </div>
  );
};

export default ChatbotTest;
