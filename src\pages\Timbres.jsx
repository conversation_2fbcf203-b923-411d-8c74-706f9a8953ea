import React, { useEffect, useState } from "react";
import axios from "axios";
import { useLanguage } from "../contexts/LanguageContext";
import StampCard from "../components/StampCard";
import StaticStampChatbot from "../components/StaticStampChatbot";

function Timbres() {
  const { t, language } = useLanguage(); // Get current language
  const [timbres, setTimbres] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Add IDs to stamp cards for chatbot scrolling
  useEffect(() => {
    if (!loading && timbres.length > 0) {
      setTimeout(() => {
        const stampCards = document.querySelectorAll('.stamp-card');
        stampCards.forEach((card, index) => {
          if (timbres[index]) {
            card.id = `stamp-${timbres[index].id}`;
          }
        });
      }, 100);
    }
  }, [loading, timbres]);

  useEffect(() => {
    const fetchTimbres = async () => {
      try {
        setLoading(true);
        const response = await axios.get("http://localhost:1337/api/timbres?populate=*");
        console.log("Données reçues:", response.data.data);
        setTimbres(response.data.data);
        setError(null);
      } catch (err) {
        console.error("Erreur API:", err);
        setError(t('stamps.error'));
      } finally {
        setLoading(false);
      }
    };

    fetchTimbres();
  }, [t]);

  return (
    <div className="timbres-page">
      <div className="page-header">
        <div className="container">
          <h1 className="page-title">{t('stamps.title')}</h1>
          <p className="page-description">
            {t('stamps.description')}
          </p>

          {/* Temporary Debug Button */}
          <button
            onClick={() => {
              console.log('🔍 DEBUG: All stamp names:');
              timbres.forEach((timbre, index) => {
                const name = timbre.attributes?.nom || timbre.attributes?.title || 'No name';
                console.log(`${index + 1}. "${name}" (ID: ${timbre.id})`);
              });
              alert(`Found ${timbres.length} stamps. Check console for details!`);
            }}
            style={{
              background: '#FFD700',
              color: '#003366',
              border: '2px solid #003366',
              padding: '10px 20px',
              borderRadius: '5px',
              cursor: 'pointer',
              margin: '10px 0'
            }}
          >
            🔍 DEBUG: Show All Stamp Names
          </button>
        </div>
      </div>

      <div className="container">
        {loading && (
          <div className="loading-message">
            <p>{t('stamps.loading')}</p>
          </div>
        )}

        {error && (
          <div className="error-message">
            <p>{error}</p>
          </div>
        )}

        {!loading && !error && timbres.length > 0 && (
          <div className="timbres-grid" id="timbres-section">
            {timbres.map((timbre) => (
              <StampCard key={timbre.id} stamp={timbre} />
            ))}
          </div>
        )}

        {!loading && !error && timbres.length === 0 && (
          <div className="no-timbres-message">
            <p>{t('stamps.noStamps')}</p>
          </div>
        )}
      </div>

      {/* Static Stamp Chatbot */}
      <StaticStampChatbot timbres={timbres} />

      <style>{`
        .timbres-page {
          min-height: 100vh;
          background: var(--light-gray);
        }

        .page-header {
          background: var(--primary-blue);
          color: var(--white);
          padding: 4rem 0 2rem;
          text-align: center;
        }

        .page-title {
          font-size: 3rem;
          font-weight: 700;
          margin-bottom: 1rem;
          color: var(--white);
        }

        .page-description {
          font-size: 1.2rem;
          color: rgba(255, 255, 255, 0.9);
          max-width: 600px;
          margin: 0 auto;
        }

        .container {
          max-width: 1200px;
          margin: 0 auto;
          padding: 0 2rem;
        }

        .timbres-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 2rem;
          padding: 3rem 0;
        }

        .loading-message,
        .error-message,
        .no-timbres-message {
          text-align: center;
          padding: 4rem 2rem;
          font-size: 1.1rem;
        }

        .loading-message p {
          color: var(--medium-gray);
        }

        .error-message p {
          color: #dc3545;
          font-weight: 600;
        }

        .no-timbres-message p {
          color: var(--medium-gray);
        }

        /* Chatbot highlight effect */
        .chatbot-highlighted {
          animation: chatbot-highlight 2s ease-in-out;
          transform: scale(1.05);
          box-shadow: 0 0 20px rgba(255, 215, 0, 0.8) !important;
          z-index: 10;
          position: relative;
        }

        @keyframes chatbot-highlight {
          0% {
            box-shadow: 0 0 0 rgba(255, 215, 0, 0.8);
            transform: scale(1);
          }
          50% {
            box-shadow: 0 0 30px rgba(255, 215, 0, 1);
            transform: scale(1.08);
          }
          100% {
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.8);
            transform: scale(1.05);
          }
        }

        @media (max-width: 768px) {
          .page-header {
            padding: 3rem 0 1.5rem;
          }

          .page-title {
            font-size: 2.5rem;
          }

          .page-description {
            font-size: 1.1rem;
          }

          .timbres-grid {
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            padding: 2rem 0;
          }
        }

        @media (max-width: 480px) {
          .page-header {
            padding: 2rem 0 1rem;
          }

          .page-title {
            font-size: 2rem;
          }

          .timbres-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
          }
        }
      `}</style>
    </div>
  );
}

export default Timbres;
