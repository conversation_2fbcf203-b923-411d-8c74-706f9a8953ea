// Script to add realistic stamps to Strapi database
// Run with: node scripts/addStampsToStrapi.js

import axios from 'axios';

const STRAPI_URL = 'http://localhost:1337';

// Realistic Tunisian stamps data
const stampsToAdd = [
  {
    nom: "Patrimoine Architectural Tunisien",
    description: [
      {
        type: "paragraph",
        children: [
          {
            type: "text",
            text: "Collection mettant en valeur l'architecture traditionnelle tunisienne avec ses mosquées, médinas et palais historiques."
          }
        ]
      }
    ],
    prix: 15.50,
    prixOriginal: 18.00,
    categorie: "Patrimoine",
    nouveau: true,
    limite: false,
    note: 5,
    enStock: true
  },
  {
    nom: "Faune Marine de Tunisie",
    description: [
      {
        type: "paragraph",
        children: [
          {
            type: "text",
            text: "Découvrez la richesse marine des côtes tunisiennes avec ses espèces endémiques et sa biodiversité exceptionnelle."
          }
        ]
      }
    ],
    prix: 12.00,
    categorie: "Nature",
    nouveau: false,
    limite: true,
    note: 4,
    enStock: true
  },
  {
    nom: "Artistes Tunisiens Contemporains",
    description: [
      {
        type: "paragraph",
        children: [
          {
            type: "text",
            text: "Hommage aux grands artistes tunisiens modernes qui ont marqué l'art contemporain maghrébin et international."
          }
        ]
      }
    ],
    prix: 20.00,
    categorie: "Art",
    nouveau: true,
    limite: true,
    note: 5,
    enStock: true
  },
  {
    nom: "Habib Bourguiba - Père de l'Indépendance",
    description: [
      {
        type: "paragraph",
        children: [
          {
            type: "text",
            text: "Série commémorative dédiée au premier président tunisien et père de l'indépendance nationale."
          }
        ]
      }
    ],
    prix: 25.00,
    categorie: "Personnalités",
    nouveau: false,
    limite: false,
    note: 5,
    enStock: true
  },
  {
    nom: "Jeux Olympiques Paris 2024",
    description: [
      {
        type: "paragraph",
        children: [
          {
            type: "text",
            text: "Participation tunisienne aux Jeux Olympiques de Paris 2024 avec nos athlètes champions."
          }
        ]
      }
    ],
    prix: 18.50,
    categorie: "Sports",
    nouveau: true,
    limite: false,
    note: 4,
    enStock: false
  },
  {
    nom: "Révolution du Jasmin",
    description: [
      {
        type: "paragraph",
        children: [
          {
            type: "text",
            text: "Commémoration de la révolution tunisienne de 2011 qui a marqué le début du printemps arabe."
          }
        ]
      }
    ],
    prix: 22.00,
    categorie: "Histoire",
    nouveau: false,
    limite: true,
    note: 5,
    enStock: true
  },
  {
    nom: "Poterie de Nabeul",
    description: [
      {
        type: "paragraph",
        children: [
          {
            type: "text",
            text: "Art traditionnel de la poterie tunisienne de Nabeul, reconnu pour ses motifs berbères et ses couleurs vives."
          }
        ]
      }
    ],
    prix: 14.00,
    categorie: "Artisanat",
    nouveau: false,
    limite: false,
    note: 4,
    enStock: true
  },
  {
    nom: "Carthage Antique",
    description: [
      {
        type: "paragraph",
        children: [
          {
            type: "text",
            text: "Sites archéologiques de l'ancienne Carthage, patrimoine mondial de l'UNESCO."
          }
        ]
      }
    ],
    prix: 16.50,
    categorie: "Archéologie",
    nouveau: true,
    limite: false,
    note: 5,
    enStock: true
  },
  {
    nom: "Oasis du Sud Tunisien",
    description: [
      {
        type: "paragraph",
        children: [
          {
            type: "text",
            text: "Paysages magnifiques des oasis du sud de la Tunisie avec leurs palmiers dattiers et leurs sources naturelles."
          }
        ]
      }
    ],
    prix: 13.50,
    categorie: "Paysages",
    nouveau: false,
    limite: false,
    note: 4,
    enStock: true
  },
  {
    nom: "Festival de Carthage",
    description: [
      {
        type: "paragraph",
        children: [
          {
            type: "text",
            text: "Célébration du festival international de Carthage, événement culturel majeur de la Méditerranée."
          }
        ]
      }
    ],
    prix: 19.00,
    categorie: "Culture",
    nouveau: true,
    limite: true,
    note: 5,
    enStock: true
  },
  {
    nom: "Médina de Tunis",
    description: [
      {
        type: "paragraph",
        children: [
          {
            type: "text",
            text: "Architecture et patrimoine de la médina historique de Tunis, inscrite au patrimoine mondial de l'UNESCO."
          }
        ]
      }
    ],
    prix: 17.00,
    categorie: "UNESCO",
    nouveau: false,
    limite: false,
    note: 5,
    enStock: true
  },
  {
    nom: "Couscous Tunisien",
    description: [
      {
        type: "paragraph",
        children: [
          {
            type: "text",
            text: "Patrimoine culinaire tunisien reconnu par l'UNESCO, symbole de la gastronomie maghrébine."
          }
        ]
      }
    ],
    prix: 11.50,
    categorie: "Gastronomie",
    nouveau: true,
    limite: false,
    note: 4,
    enStock: true
  },
  {
    nom: "Djerba l'Île des Rêves",
    description: [
      {
        type: "paragraph",
        children: [
          {
            type: "text",
            text: "Beauté et traditions de l'île de Djerba, destination touristique emblématique de la Tunisie."
          }
        ]
      }
    ],
    prix: 15.00,
    categorie: "Tourisme",
    nouveau: false,
    limite: true,
    note: 4,
    enStock: true
  },
  {
    nom: "Jasmin de Tunisie",
    description: [
      {
        type: "paragraph",
        children: [
          {
            type: "text",
            text: "Fleur nationale et symbole de la Tunisie, emblème de la révolution du jasmin."
          }
        ]
      }
    ],
    prix: 12.50,
    categorie: "Symboles",
    nouveau: true,
    limite: false,
    note: 4,
    enStock: true
  },
  {
    nom: "Indépendance de la Tunisie",
    description: [
      {
        type: "paragraph",
        children: [
          {
            type: "text",
            text: "Commémoration de l'indépendance tunisienne du 20 mars 1956, jour historique de la nation."
          }
        ]
      }
    ],
    prix: 24.00,
    categorie: "Histoire",
    nouveau: false,
    limite: true,
    note: 5,
    enStock: true
  }
];

async function addStampToStrapi(stamp) {
  try {
    const response = await axios.post(`${STRAPI_URL}/api/timbres`, {
      data: {
        ...stamp,
        publishedAt: new Date().toISOString()
      }
    });
    
    console.log(`✅ Added: ${stamp.nom}`);
    return { success: true, stamp: stamp.nom, id: response.data.data.id };
  } catch (error) {
    console.error(`❌ Failed to add ${stamp.nom}:`, error.response?.data || error.message);
    return { success: false, stamp: stamp.nom, error: error.message };
  }
}

async function addAllStamps() {
  console.log('🚀 Starting to add stamps to Strapi...');
  console.log(`📋 Will add ${stampsToAdd.length} stamps`);
  
  const results = [];
  
  for (const stamp of stampsToAdd) {
    const result = await addStampToStrapi(stamp);
    results.push(result);
    
    // Small delay to avoid overwhelming the server
    await new Promise(resolve => setTimeout(resolve, 500));
  }
  
  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  
  console.log('\n🎉 Summary:');
  console.log(`✅ Successfully added: ${successful} stamps`);
  console.log(`❌ Failed to add: ${failed} stamps`);
  
  if (failed > 0) {
    console.log('\n❌ Failed stamps:');
    results.filter(r => !r.success).forEach(r => {
      console.log(`  - ${r.stamp}: ${r.error}`);
    });
  }
  
  console.log('\n🔄 You can now refresh your frontend to see the new stamps!');
}

// Check if Strapi is running
async function checkStrapi() {
  try {
    await axios.get(`${STRAPI_URL}/api/timbres`);
    console.log('✅ Strapi is running');
    return true;
  } catch (error) {
    console.error('❌ Strapi is not running. Please start it with: npm run dev');
    return false;
  }
}

// Main execution
async function main() {
  const strapiRunning = await checkStrapi();
  if (strapiRunning) {
    await addAllStamps();
  }
}

main().catch(console.error);
