import React, { useState } from 'react';
import { syncAllStamps, frontendStamps } from '../utils/syncStampsData';

const DataSyncButton = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [syncResult, setSyncResult] = useState(null);

  const handleSync = async () => {
    setIsLoading(true);
    setSyncResult(null);
    
    try {
      console.log('🚀 Starting data synchronization...');
      const result = await syncAllStamps();
      
      setSyncResult({
        success: true,
        message: `✅ Synchronization complete! Now have ${result.length} stamps in Strapi.`,
        totalStamps: result.length
      });
      
      // Refresh the page after sync to show new data
      setTimeout(() => {
        window.location.reload();
      }, 2000);
      
    } catch (error) {
      console.error('Sync error:', error);
      setSyncResult({
        success: false,
        message: `❌ Synchronization failed: ${error.message}`
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div style={{
      background: '#f8f9fa',
      border: '2px solid #FFD700',
      borderRadius: '10px',
      padding: '20px',
      margin: '20px 0',
      textAlign: 'center'
    }}>
      <h3 style={{ color: '#003366', margin: '0 0 15px 0' }}>
        🔄 Data Synchronization
      </h3>
      
      <p style={{ margin: '0 0 15px 0', color: '#666' }}>
        Sync {frontendStamps.length} frontend stamps with Strapi backend
      </p>
      
      <button
        onClick={handleSync}
        disabled={isLoading}
        style={{
          background: isLoading ? '#ccc' : '#FFD700',
          color: '#003366',
          border: '2px solid #003366',
          padding: '12px 24px',
          borderRadius: '8px',
          cursor: isLoading ? 'not-allowed' : 'pointer',
          fontSize: '16px',
          fontWeight: 'bold',
          transition: 'all 0.3s ease'
        }}
        onMouseEnter={(e) => {
          if (!isLoading) {
            e.target.style.background = '#FFC107';
            e.target.style.transform = 'translateY(-2px)';
          }
        }}
        onMouseLeave={(e) => {
          if (!isLoading) {
            e.target.style.background = '#FFD700';
            e.target.style.transform = 'translateY(0)';
          }
        }}
      >
        {isLoading ? '🔄 Synchronizing...' : '🚀 Sync Frontend → Backend'}
      </button>
      
      {syncResult && (
        <div style={{
          marginTop: '15px',
          padding: '10px',
          borderRadius: '5px',
          background: syncResult.success ? '#d4edda' : '#f8d7da',
          color: syncResult.success ? '#155724' : '#721c24',
          border: `1px solid ${syncResult.success ? '#c3e6cb' : '#f5c6cb'}`
        }}>
          {syncResult.message}
        </div>
      )}
      
      <div style={{ 
        marginTop: '15px', 
        fontSize: '14px', 
        color: '#666',
        textAlign: 'left'
      }}>
        <strong>What this does:</strong>
        <ul style={{ margin: '5px 0', paddingLeft: '20px' }}>
          <li>✅ Adds missing frontend stamps to Strapi</li>
          <li>✅ Preserves existing Strapi data</li>
          <li>✅ Makes chatbot work with all stamps</li>
          <li>✅ Refreshes page to show synced data</li>
        </ul>
      </div>
    </div>
  );
};

export default DataSyncButton;
