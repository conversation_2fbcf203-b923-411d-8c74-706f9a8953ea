// INTEGRATION GUIDE FOR MULTILINGUAL CHATBOT
// ==========================================

import React, { useState, useEffect } from 'react';
import axios from 'axios';
import { useLanguage } from '../contexts/LanguageContext';
import MultilingualChatbot from '../components/MultilingualChatbot';

// EXAMPLE 1: Integration in HomePage
const HomePage = () => {
  const { language } = useLanguage(); // Get current language from your context
  const [timbres, setTimbres] = useState([]);
  const [loading, setLoading] = useState(true);

  // Fetch stamps data
  useEffect(() => {
    const fetchStamps = async () => {
      try {
        const response = await axios.get('http://localhost:1337/api/timbres?populate=*');
        setTimbres(response.data.data);
      } catch (error) {
        console.error('Error fetching stamps:', error);
      } finally {
        setLoading(false);
      }
    };
    fetchStamps();
  }, []);

  // IMPORTANT: Add IDs to stamp cards for scrolling functionality
  useEffect(() => {
    if (!loading && timbres.length > 0) {
      setTimeout(() => {
        const stampCards = document.querySelectorAll('.stamp-card, .timbre-card');
        stampCards.forEach((card, index) => {
          if (timbres[index]) {
            card.id = `stamp-${timbres[index].id}`;
          }
        });
      }, 100);
    }
  }, [loading, timbres]);

  return (
    <div className="homepage">
      {/* Your existing content */}
      <section id="hero">
        {/* Hero content */}
      </section>
      
      <section id="timbres-section">
        {/* Your stamps grid */}
        <div className="stamps-grid">
          {timbres.map((timbre) => (
            <div key={timbre.id} className="stamp-card">
              {/* Your stamp card content */}
            </div>
          ))}
        </div>
      </section>

      {/* ADD THE CHATBOT HERE */}
      <MultilingualChatbot 
        timbres={timbres} 
        language={language} // Pass current language
      />
    </div>
  );
};

// EXAMPLE 2: If you don't have LanguageContext
const HomePageWithoutContext = () => {
  const [timbres, setTimbres] = useState([]);
  const [currentLanguage, setCurrentLanguage] = useState('fr'); // Default to French

  // You can change language with buttons or detect from browser
  const handleLanguageChange = (lang) => {
    setCurrentLanguage(lang);
  };

  return (
    <div>
      {/* Language switcher */}
      <div className="language-switcher">
        <button onClick={() => handleLanguageChange('fr')}>Français</button>
        <button onClick={() => handleLanguageChange('ar')}>العربية</button>
        <button onClick={() => handleLanguageChange('en')}>English</button>
      </div>

      {/* Your content */}
      
      {/* Chatbot */}
      <MultilingualChatbot 
        timbres={timbres} 
        language={currentLanguage}
      />
    </div>
  );
};

// REQUIRED SETUP STEPS:
// =====================

// 1. Install Framer Motion (if not already installed):
//    npm install framer-motion

// 2. Make sure your stamp cards have the class 'stamp-card' or 'timbre-card'

// 3. Add an id="timbres-section" to your stamps container

// 4. Ensure your timbres data structure matches:
//    {
//      id: number,
//      attributes: {
//        title: string,
//        description: string,
//        category: string
//      }
//    }

// 5. Pass the current language as a prop (fr, ar, or en)

// FEATURES INCLUDED:
// ==================

// ✅ Multilingual support (French, Arabic, English)
// ✅ RTL support for Arabic
// ✅ Stamp search and highlighting
// ✅ Smooth animations with Framer Motion
// ✅ Minimize/maximize functionality
// ✅ Conversation persistence
// ✅ Quick action buttons
// ✅ Mobile responsive
// ✅ Tunisia Post color scheme
// ✅ Keyword detection in multiple languages
// ✅ Smooth scrolling to stamps/sections

// CUSTOMIZATION:
// ==============

// You can customize the chatbot by:
// 1. Modifying the content object for different responses
// 2. Adding new quick actions
// 3. Changing colors in the inline styles
// 4. Adding new keyword detection patterns
// 5. Modifying animations in Framer Motion props

export default HomePage;
