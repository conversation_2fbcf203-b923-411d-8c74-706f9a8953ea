import React, { useState, useRef, useEffect } from 'react';

const StaticStampChatbot = ({ timbres = [] }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [messages, setMessages] = useState([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef(null);

  // Welcome message when chat opens
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      setTimeout(() => {
        addBotMessage(`Bonjour ! 👋 Je suis votre assistant pour explorer notre collection de ${timbres.length} timbres tunisiens.

🔍 Tapez le nom d'un timbre pour le trouver
📋 Tapez "debug" pour voir tous les timbres
🎯 Tapez "tous" pour voir la collection

Comment puis-je vous aider ?`);
      }, 500);
    }
  }, [isOpen, messages.length, timbres.length]);

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, isTyping]);

  const addBotMessage = (text) => {
    setMessages(prev => [...prev, {
      id: Date.now(),
      text,
      sender: 'bot',
      timestamp: new Date()
    }]);
  };

  const addUserMessage = (text) => {
    setMessages(prev => [...prev, {
      id: Date.now(),
      text,
      sender: 'user',
      timestamp: new Date()
    }]);
  };

  const findStampByName = (searchName) => {
    const normalizedSearch = searchName.toLowerCase().trim();

    // Debug: Log all available stamp names
    console.log('🔍 Searching for:', normalizedSearch);
    console.log('📋 Available stamps:', timbres.map(t => ({
      id: t.id,
      nom: t.attributes?.nom || t.attributes?.title || 'No name',
      attributes: t.attributes
    })));

    // First try exact match
    let foundStamp = timbres.find(timbre => {
      const stampName = (timbre.attributes?.nom || timbre.attributes?.title || '').toLowerCase().trim();
      console.log(`🔎 Exact match: "${normalizedSearch}" === "${stampName}" ?`, stampName === normalizedSearch);
      return stampName === normalizedSearch;
    });

    // If no exact match, try partial match (contains)
    if (!foundStamp) {
      foundStamp = timbres.find(timbre => {
        const stampName = (timbre.attributes?.nom || timbre.attributes?.title || '').toLowerCase().trim();
        const matches = stampName.includes(normalizedSearch) || normalizedSearch.includes(stampName);
        console.log(`🔎 Partial match: "${stampName}" contains "${normalizedSearch}" ?`, matches);
        return matches;
      });
    }

    console.log('✅ Found stamp:', foundStamp);
    return foundStamp;
  };

  const scrollToStamp = (stampId) => {
    // Check if we're on the Timbres page
    const currentPath = window.location.pathname;

    if (currentPath !== '/timbres') {
      // Navigate to Timbres page first, then scroll to stamp
      window.location.href = `/timbres#timbre-${stampId}`;
      return;
    }

    // We're already on Timbres page, find and scroll to the stamp
    const element = document.getElementById(`timbre-${stampId}`);
    if (element) {
      // Scroll to element
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });

      // Add highlight class with stronger styling
      element.classList.add('highlight');

      // Also add a temporary glow effect
      element.style.transition = 'all 0.5s ease';
      element.style.transform = 'scale(1.02)';
      element.style.zIndex = '1000';

      // Remove effects after 4 seconds
      setTimeout(() => {
        element.classList.remove('highlight');
        element.style.transform = '';
        element.style.zIndex = '';
      }, 4000);
    } else {
      // If element not found, try again after a short delay (for loading)
      setTimeout(() => {
        const retryElement = document.getElementById(`timbre-${stampId}`);
        if (retryElement) {
          retryElement.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
          });
          retryElement.classList.add('highlight');
          setTimeout(() => {
            retryElement.classList.remove('highlight');
          }, 4000);
        }
      }, 1000);
    }
  };

  const processMessage = (message) => {
    const lowerMessage = message.toLowerCase().trim();

    // Special commands
    if (lowerMessage === 'debug' || lowerMessage === 'list' || lowerMessage === 'aide debug') {
      const stampList = timbres.map(t => {
        const name = t.attributes?.nom || t.attributes?.title || 'Sans nom';
        return `• "${name}" (ID: ${t.id})`;
      }).join('\n');

      return `🔍 DEBUG - Timbres disponibles (${timbres.length} total):\n\n${stampList}\n\nTapez exactement l'un de ces noms pour le trouver.`;
    }

    // Show all stamps command
    if (lowerMessage.includes('tous') || lowerMessage.includes('all') || lowerMessage.includes('collection')) {
      const timbresSection = document.getElementById('timbres-section');
      if (timbresSection) {
        timbresSection.scrollIntoView({ behavior: 'smooth' });
        return `Voici notre collection complète ! Nous avons ${timbres.length} timbres magnifiques à découvrir. 📮`;
      }
    }

    // About/contact commands
    if (lowerMessage.includes('site') || lowerMessage.includes('about') || lowerMessage.includes('propos')) {
      return "Ce site présente la collection officielle des timbres tunisiens. Vous pouvez explorer notre patrimoine philatélique et découvrir l'histoire de nos timbres ! 🇹🇳";
    }

    if (lowerMessage.includes('contact') || lowerMessage.includes('aide') || lowerMessage.includes('help')) {
      return "Pour nous contacter : visitez la section Contact de notre site. Notre équipe sera ravie de vous aider ! 📞";
    }

    // Greetings
    if (lowerMessage.includes('bonjour') || lowerMessage.includes('salut') || lowerMessage.includes('hello')) {
      return "Bonjour ! Bienvenue dans notre collection de timbres tunisiens. Tapez le nom d'un timbre pour le trouver ou 'debug' pour voir tous les timbres disponibles. 😊";
    }

    // Search for stamps
    const foundStamp = findStampByName(message);

    if (foundStamp) {
      // Scroll to stamp and highlight it
      scrollToStamp(foundStamp.id);
      const stampName = foundStamp.attributes?.nom || foundStamp.attributes?.title || 'ce timbre';
      const currentPath = window.location.pathname;

      if (currentPath !== '/timbres') {
        return `✅ Timbre "${stampName}" trouvé ! 🎯 Redirection vers la page Timbres pour vous le montrer...`;
      } else {
        return `✅ Voici le timbre "${stampName}" ! Je l'ai mis en évidence pour vous. 👇`;
      }
    } else {
      // Enhanced help message with suggestions
      return `❌ Aucun timbre trouvé pour "${message}".

🔍 Commandes disponibles :
• Tapez le nom exact d'un timbre
• "debug" - Voir tous les timbres
• "tous" - Voir toute la collection
• "contact" - Informations de contact

💡 Exemples de timbres :
• "Poterie de Nabeul"
• "Révolution du Jasmin"
• "Carthage Antique"

Que souhaitez-vous faire ? 🤔`;
    }
  };

  const handleSendMessage = () => {
    if (!inputValue.trim()) return;

    addUserMessage(inputValue);
    setIsTyping(true);

    setTimeout(() => {
      const response = processMessage(inputValue);
      addBotMessage(response);
      setIsTyping(false);
    }, 1000);

    setInputValue('');
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter') {
      handleSendMessage();
    }
  };

  // Chat button (when closed)
  if (!isOpen) {
    return (
      <>
        <button
          onClick={() => setIsOpen(true)}
          className="chatbot-button"
        >
          💬
        </button>
        
        <style>{`
          .chatbot-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            background: #FFD700;
            color: #003366;
            border: 3px solid #003366;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            box-shadow: 0 4px 20px rgba(255, 215, 0, 0.4);
            z-index: 9999;
            transition: all 0.3s ease;
          }

          .chatbot-button:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 25px rgba(255, 215, 0, 0.6);
          }

          .highlight {
            border: 3px solid #FFD700 !important;
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.8) !important;
            transition: all 0.3s ease !important;
          }

          @media (max-width: 768px) {
            .chatbot-button {
              bottom: 15px;
              right: 15px;
              width: 50px;
              height: 50px;
              font-size: 20px;
            }
          }
        `}</style>
      </>
    );
  }

  // Chat window (when open)
  return (
    <>
      <div className={`chatbot-window ${isMinimized ? 'minimized' : ''}`}>
        {/* Header */}
        <div className="chatbot-header">
          <div className="chatbot-title">
            <div className="chatbot-avatar">🤖</div>
            <div>
              <h3>Assistant Timbres</h3>
              <span className="chatbot-status">En ligne</span>
            </div>
          </div>
          <div className="chatbot-actions">
            <button
              onClick={() => setIsMinimized(!isMinimized)}
              className="chatbot-action-btn"
              title="Réduire"
            >
              −
            </button>
            <button
              onClick={() => setIsOpen(false)}
              className="chatbot-action-btn"
              title="Fermer"
            >
              ✕
            </button>
          </div>
        </div>

        {!isMinimized && (
          <>
            {/* Messages */}
            <div className="chatbot-messages">
              {messages.map((message) => (
                <div key={message.id} className={`message-bubble ${message.sender}-message`}>
                  <div className="message-content">
                    <p>{message.text}</p>
                    <span className="message-time">
                      {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </span>
                  </div>
                </div>
              ))}
              
              {isTyping && (
                <div className="typing-indicator">
                  <div className="typing-bubble">
                    <div className="typing-dots">
                      <span></span>
                      <span></span>
                      <span></span>
                    </div>
                  </div>
                </div>
              )}
              
              <div ref={messagesEndRef} />
            </div>

            {/* Input */}
            <div className="chat-input-form">
              <div className="chat-input-container">
                <input
                  type="text"
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder="Tapez le nom exact du timbre..."
                  className="chat-input"
                  disabled={isTyping}
                />
                <button
                  onClick={handleSendMessage}
                  className="chat-send-btn"
                  disabled={!inputValue.trim() || isTyping}
                >
                  ➤
                </button>
              </div>
            </div>
          </>
        )}
      </div>

      <style>{`
        .chatbot-window {
          position: fixed;
          bottom: 20px;
          right: 20px;
          width: 350px;
          height: 500px;
          background: white;
          border-radius: 15px;
          box-shadow: 0 10px 40px rgba(0, 51, 102, 0.2);
          display: flex;
          flex-direction: column;
          z-index: 9999;
          overflow: hidden;
          transition: all 0.3s ease;
        }

        .chatbot-window.minimized {
          height: 60px;
        }

        .chatbot-header {
          background: linear-gradient(135deg, #003366 0%, #004080 100%);
          color: white;
          padding: 1rem;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .chatbot-title {
          display: flex;
          align-items: center;
          gap: 0.75rem;
        }

        .chatbot-avatar {
          width: 35px;
          height: 35px;
          background: #FFD700;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.1rem;
        }

        .chatbot-title h3 {
          margin: 0;
          font-size: 0.95rem;
          color: white;
        }

        .chatbot-status {
          font-size: 0.75rem;
          opacity: 0.9;
        }

        .chatbot-actions {
          display: flex;
          gap: 0.5rem;
        }

        .chatbot-action-btn {
          background: transparent;
          border: none;
          color: white;
          cursor: pointer;
          padding: 0.25rem;
          border-radius: 4px;
          transition: all 0.3s ease;
          font-size: 16px;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .chatbot-action-btn:hover {
          background: rgba(255, 255, 255, 0.1);
        }

        .chatbot-messages {
          flex: 1;
          padding: 1rem;
          overflow-y: auto;
          display: flex;
          flex-direction: column;
          gap: 0.75rem;
          max-height: 350px;
        }

        .chatbot-messages::-webkit-scrollbar {
          width: 4px;
        }

        .chatbot-messages::-webkit-scrollbar-track {
          background: #f1f1f1;
        }

        .chatbot-messages::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 2px;
        }

        .message-bubble {
          display: flex;
          margin-bottom: 0.5rem;
        }

        .bot-message {
          justify-content: flex-start;
        }

        .user-message {
          justify-content: flex-end;
        }

        .message-content {
          max-width: 80%;
          padding: 0.75rem 1rem;
          border-radius: 18px;
          word-wrap: break-word;
        }

        .bot-message .message-content {
          background: #f8f9fa;
          color: #343a40;
          border-bottom-left-radius: 4px;
        }

        .user-message .message-content {
          background: #003366;
          color: white;
          border-bottom-right-radius: 4px;
        }

        .message-content p {
          margin: 0;
          font-size: 0.9rem;
          line-height: 1.4;
          white-space: pre-line;
        }

        .message-time {
          font-size: 0.7rem;
          opacity: 0.7;
          display: block;
          margin-top: 0.25rem;
        }

        .bot-message .message-time {
          text-align: left;
        }

        .user-message .message-time {
          text-align: right;
        }

        .typing-indicator {
          display: flex;
          justify-content: flex-start;
        }

        .typing-bubble {
          background: #f8f9fa;
          border-radius: 18px;
          padding: 0.75rem 1rem;
          max-width: 60px;
        }

        .typing-dots {
          display: flex;
          gap: 4px;
          align-items: center;
        }

        .typing-dots span {
          width: 6px;
          height: 6px;
          background: #6c757d;
          border-radius: 50%;
          animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
        .typing-dots span:nth-child(2) { animation-delay: -0.16s; }

        @keyframes typing {
          0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
          40% { transform: scale(1); opacity: 1; }
        }

        .chat-input-form {
          padding: 1rem;
          border-top: 1px solid #dee2e6;
          background: white;
        }

        .chat-input-container {
          display: flex;
          gap: 0.5rem;
          align-items: center;
        }

        .chat-input {
          flex: 1;
          padding: 0.75rem 1rem;
          border: 1px solid #dee2e6;
          border-radius: 25px;
          font-size: 0.9rem;
          outline: none;
          transition: all 0.3s ease;
          background: #f8f9fa;
        }

        .chat-input:focus {
          border-color: #003366;
          background: white;
        }

        .chat-input:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .chat-send-btn {
          width: 40px;
          height: 40px;
          background: #003366;
          color: white;
          border: none;
          border-radius: 50%;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;
          font-size: 16px;
        }

        .chat-send-btn:hover:not(:disabled) {
          background: #004080;
          transform: scale(1.05);
        }

        .chat-send-btn:disabled {
          opacity: 0.5;
          cursor: not-allowed;
          transform: none;
        }

        .highlight {
          border: 4px solid #FFD700 !important;
          box-shadow: 0 0 30px rgba(255, 215, 0, 0.9), 0 0 60px rgba(255, 215, 0, 0.5) !important;
          background: linear-gradient(135deg, rgba(255, 215, 0, 0.1), rgba(255, 215, 0, 0.05)) !important;
          transition: all 0.5s ease !important;
          position: relative !important;
          z-index: 999 !important;
        }

        .highlight::before {
          content: "🎯 TROUVÉ !";
          position: absolute;
          top: -15px;
          left: 50%;
          transform: translateX(-50%);
          background: #FFD700;
          color: #003366;
          padding: 5px 15px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: bold;
          z-index: 1000;
          animation: bounce 0.5s ease;
        }

        @keyframes bounce {
          0%, 20%, 50%, 80%, 100% { transform: translateX(-50%) translateY(0); }
          40% { transform: translateX(-50%) translateY(-10px); }
          60% { transform: translateX(-50%) translateY(-5px); }
        }

        @media (max-width: 768px) {
          .chatbot-window {
            bottom: 0;
            right: 0;
            width: 100vw;
            height: 100vh;
            border-radius: 0;
          }

          .chatbot-window.minimized {
            height: 60px;
            width: 350px;
            bottom: 20px;
            right: 20px;
            border-radius: 15px;
          }
        }
      `}</style>
    </>
  );
};

export default StaticStampChatbot;
