import React, { useState, useRef, useEffect } from 'react';
import { MessageCircle, X, Minus, Send, Search, Info, Phone } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

const StaticChatbot = ({ timbres = [], onScrollToStamp }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [messages, setMessages] = useState([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef(null);
  const { t, isRTL } = useLanguage();

  // Initialize with welcome message
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      setTimeout(() => {
        addBotMessage("Bonjour ! Je suis votre assistant pour explorer notre collection de timbres tunisiens. Comment puis-je vous aider ?");
      }, 500);
    }
  }, [isOpen]);

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, isTyping]);

  const addBotMessage = (text) => {
    setMessages(prev => [...prev, {
      id: Date.now(),
      text,
      sender: 'bot',
      timestamp: new Date()
    }]);
  };

  const addUserMessage = (text) => {
    setMessages(prev => [...prev, {
      id: Date.now(),
      text,
      sender: 'user',
      timestamp: new Date()
    }]);
  };

  const findStampByName = (searchTerm) => {
    const term = searchTerm.toLowerCase();
    return timbres.find(timbre => {
      const title = timbre.attributes?.title?.toLowerCase() || '';
      const description = timbre.attributes?.description?.toLowerCase() || '';
      return title.includes(term) || description.includes(term);
    });
  };

  const scrollToStamp = (stampId) => {
    const element = document.getElementById(`stamp-${stampId}`);
    if (element) {
      element.scrollIntoView({ 
        behavior: 'smooth', 
        block: 'center' 
      });
      // Highlight effect
      element.style.boxShadow = '0 0 20px rgba(255, 215, 0, 0.8)';
      element.style.transform = 'scale(1.05)';
      setTimeout(() => {
        element.style.boxShadow = '';
        element.style.transform = '';
      }, 2000);
      
      if (onScrollToStamp) {
        onScrollToStamp(stampId);
      }
    }
  };

  const processMessage = (message) => {
    const lowerMessage = message.toLowerCase();
    
    // Search for specific stamps
    if (lowerMessage.includes('timbre') || lowerMessage.includes('voir') || lowerMessage.includes('cherche')) {
      const stampKeywords = ['patrimoine', 'tunisien', 'nature', 'carthage', 'indépendance', 'sport'];
      const foundKeyword = stampKeywords.find(keyword => lowerMessage.includes(keyword));
      
      if (foundKeyword) {
        const foundStamp = findStampByName(foundKeyword);
        if (foundStamp) {
          scrollToStamp(foundStamp.id);
          return `Parfait ! Je vous montre le timbre "${foundStamp.attributes?.title}". Je l'ai mis en évidence pour vous ! 🎯`;
        } else {
          return `Je n'ai pas trouvé de timbre correspondant à "${foundKeyword}". Voulez-vous voir tous nos timbres disponibles ?`;
        }
      }
    }

    // Show all stamps
    if (lowerMessage.includes('tous') || lowerMessage.includes('all') || lowerMessage.includes('collection')) {
      const timbresSection = document.getElementById('timbres-section');
      if (timbresSection) {
        timbresSection.scrollIntoView({ behavior: 'smooth' });
      }
      return `Voici notre collection complète ! Nous avons ${timbres.length} timbres magnifiques à découvrir. Faites défiler pour les explorer ! 📮`;
    }

    // About the site
    if (lowerMessage.includes('site') || lowerMessage.includes('about') || lowerMessage.includes('propos')) {
      return "Ce site présente la collection officielle des timbres tunisiens. Vous pouvez explorer notre patrimoine philatélique, découvrir l'histoire de nos timbres et ajouter vos favoris ! 🇹🇳";
    }

    // Contact info
    if (lowerMessage.includes('contact') || lowerMessage.includes('aide') || lowerMessage.includes('help')) {
      return "Pour nous contacter : visitez la section Contact de notre site ou utilisez le formulaire en bas de page. Notre équipe sera ravie de vous aider ! 📞";
    }

    // Greetings
    if (lowerMessage.includes('bonjour') || lowerMessage.includes('salut') || lowerMessage.includes('hello')) {
      return "Bonjour ! Bienvenue dans notre collection de timbres tunisiens. Que souhaitez-vous découvrir aujourd'hui ? 😊";
    }

    // Thanks
    if (lowerMessage.includes('merci') || lowerMessage.includes('thank')) {
      return "Je vous en prie ! N'hésitez pas si vous avez d'autres questions sur notre collection ! 🙏";
    }

    // Default response
    return "Je peux vous aider à : \n• Trouver un timbre spécifique \n• Voir toute la collection \n• Obtenir des informations sur le site \n• Vous donner les contacts \n\nQue souhaitez-vous faire ? 🤔";
  };

  const handleSendMessage = () => {
    if (!inputValue.trim()) return;

    addUserMessage(inputValue);
    setIsTyping(true);

    setTimeout(() => {
      const response = processMessage(inputValue);
      addBotMessage(response);
      setIsTyping(false);
    }, 1000 + Math.random() * 1000);

    setInputValue('');
  };

  const handleQuickAction = (action) => {
    switch (action) {
      case 'show-all':
        addUserMessage("Montrez-moi tous les timbres");
        setTimeout(() => {
          const response = processMessage("tous les timbres");
          addBotMessage(response);
        }, 500);
        break;
      case 'about':
        addUserMessage("Qu'est-ce que ce site ?");
        setTimeout(() => {
          addBotMessage("Ce site présente la collection officielle des timbres tunisiens. Vous pouvez explorer notre patrimoine philatélique, découvrir l'histoire de nos timbres et ajouter vos favoris ! 🇹🇳");
        }, 500);
        break;
      case 'contact':
        addUserMessage("Informations de contact");
        setTimeout(() => {
          addBotMessage("Pour nous contacter : visitez la section Contact de notre site ou utilisez le formulaire en bas de page. Notre équipe sera ravie de vous aider ! 📞");
        }, 500);
        break;
    }
  };

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className="static-chatbot-toggle"
        aria-label="Ouvrir l'assistant"
      >
        <MessageCircle size={24} />
        <span className="helper-badge">?</span>
      </button>
    );
  }

  return (
    <div className={`static-chatbot-window ${isMinimized ? 'minimized' : ''}`}>
      {/* Header */}
      <div className="chatbot-header">
        <div className="chatbot-title">
          <div className="chatbot-avatar">🤖</div>
          <div>
            <h3>Assistant Timbres</h3>
            <span className="chatbot-status">
              <span className="status-dot"></span>
              En ligne
            </span>
          </div>
        </div>
        <div className="chatbot-actions">
          <button
            onClick={() => setIsMinimized(!isMinimized)}
            className="chatbot-action-btn"
            title="Réduire"
          >
            <Minus size={16} />
          </button>
          <button
            onClick={() => setIsOpen(false)}
            className="chatbot-action-btn"
            title="Fermer"
          >
            <X size={16} />
          </button>
        </div>
      </div>

      {!isMinimized && (
        <>
          {/* Messages */}
          <div className="chatbot-messages">
            {messages.map((message) => (
              <div key={message.id} className={`message-bubble ${message.sender}-message`}>
                <div className="message-content">
                  <p>{message.text}</p>
                  <span className="message-time">
                    {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </span>
                </div>
              </div>
            ))}
            
            {isTyping && (
              <div className="typing-indicator">
                <div className="typing-bubble">
                  <div className="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                </div>
              </div>
            )}
            
            <div ref={messagesEndRef} />
          </div>

          {/* Quick Actions */}
          {messages.length <= 1 && (
            <div className="quick-actions">
              <div className="quick-actions-grid">
                <button onClick={() => handleQuickAction('show-all')} className="quick-action-btn">
                  <Search size={14} />
                  Voir tous les timbres
                </button>
                <button onClick={() => handleQuickAction('about')} className="quick-action-btn">
                  <Info size={14} />
                  À propos du site
                </button>
                <button onClick={() => handleQuickAction('contact')} className="quick-action-btn">
                  <Phone size={14} />
                  Contact
                </button>
              </div>
            </div>
          )}

          {/* Input */}
          <div className="chat-input-form">
            <div className="chat-input-container">
              <input
                type="text"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                placeholder="Tapez votre message..."
                className="chat-input"
                disabled={isTyping}
              />
              <button
                onClick={handleSendMessage}
                className="chat-send-btn"
                disabled={!inputValue.trim() || isTyping}
              >
                <Send size={16} />
              </button>
            </div>
          </div>
        </>
      )}

      <style>{`
        .static-chatbot-toggle {
          position: fixed;
          bottom: 20px;
          ${isRTL ? 'left: 20px;' : 'right: 20px;'}
          width: 60px;
          height: 60px;
          background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
          color: #003366;
          border: 3px solid #003366;
          border-radius: 50%;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 4px 20px rgba(255, 215, 0, 0.4);
          transition: all 0.3s ease;
          z-index: 9999;
          animation: pulse 2s infinite;
        }

        .static-chatbot-toggle:hover {
          transform: scale(1.1);
          box-shadow: 0 6px 25px rgba(255, 215, 0, 0.6);
        }

        .helper-badge {
          position: absolute;
          top: -5px;
          right: -5px;
          background: #FF4444;
          color: white;
          border-radius: 50%;
          width: 20px;
          height: 20px;
          font-size: 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: bold;
        }

        .static-chatbot-window {
          position: fixed;
          bottom: 20px;
          ${isRTL ? 'left: 20px;' : 'right: 20px;'}
          width: 350px;
          height: 500px;
          background: white;
          border-radius: 15px;
          box-shadow: 0 10px 40px rgba(0, 51, 102, 0.2);
          display: flex;
          flex-direction: column;
          z-index: 9999;
          overflow: hidden;
          transition: all 0.3s ease;
        }

        .static-chatbot-window.minimized {
          height: 60px;
        }

        .chatbot-header {
          background: linear-gradient(135deg, #003366 0%, #004080 100%);
          color: white;
          padding: 1rem;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .chatbot-title {
          display: flex;
          align-items: center;
          gap: 0.75rem;
        }

        .chatbot-avatar {
          width: 35px;
          height: 35px;
          background: #FFD700;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.1rem;
        }

        .chatbot-title h3 {
          margin: 0;
          font-size: 0.95rem;
          color: white;
        }

        .chatbot-status {
          font-size: 0.75rem;
          opacity: 0.9;
          display: flex;
          align-items: center;
          gap: 0.25rem;
        }

        .status-dot {
          width: 6px;
          height: 6px;
          background: #4CAF50;
          border-radius: 50%;
          animation: pulse-dot 2s infinite;
        }

        .chatbot-actions {
          display: flex;
          gap: 0.5rem;
        }

        .chatbot-action-btn {
          background: transparent;
          border: none;
          color: white;
          cursor: pointer;
          padding: 0.25rem;
          border-radius: 4px;
          transition: all 0.3s ease;
        }

        .chatbot-action-btn:hover {
          background: rgba(255, 255, 255, 0.1);
        }

        .chatbot-messages {
          flex: 1;
          padding: 1rem;
          overflow-y: auto;
          display: flex;
          flex-direction: column;
          gap: 0.75rem;
        }

        .message-bubble {
          display: flex;
          margin-bottom: 0.5rem;
        }

        .bot-message {
          justify-content: flex-start;
        }

        .user-message {
          justify-content: flex-end;
        }

        .message-content {
          max-width: 80%;
          padding: 0.75rem 1rem;
          border-radius: 18px;
          word-wrap: break-word;
        }

        .bot-message .message-content {
          background: #f8f9fa;
          color: #343a40;
          border-bottom-left-radius: 4px;
        }

        .user-message .message-content {
          background: #003366;
          color: white;
          border-bottom-right-radius: 4px;
        }

        .message-content p {
          margin: 0;
          font-size: 0.9rem;
          line-height: 1.4;
          white-space: pre-line;
        }

        .message-time {
          font-size: 0.7rem;
          opacity: 0.7;
          display: block;
          margin-top: 0.25rem;
          text-align: right;
        }

        .bot-message .message-time {
          text-align: left;
        }

        .typing-indicator {
          display: flex;
          justify-content: flex-start;
        }

        .typing-bubble {
          background: #f8f9fa;
          border-radius: 18px;
          padding: 0.75rem 1rem;
          max-width: 60px;
        }

        .typing-dots {
          display: flex;
          gap: 4px;
          align-items: center;
        }

        .typing-dots span {
          width: 6px;
          height: 6px;
          background: #6c757d;
          border-radius: 50%;
          animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
        .typing-dots span:nth-child(2) { animation-delay: -0.16s; }

        .quick-actions {
          padding: 1rem;
          border-top: 1px solid #dee2e6;
          background: #f8f9fa;
        }

        .quick-actions-grid {
          display: grid;
          grid-template-columns: 1fr;
          gap: 0.5rem;
        }

        .quick-action-btn {
          padding: 0.75rem;
          background: white;
          border: 1px solid #dee2e6;
          border-radius: 8px;
          font-size: 0.85rem;
          color: #003366;
          cursor: pointer;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          gap: 0.5rem;
          text-align: left;
        }

        .quick-action-btn:hover {
          background: #FFD700;
          border-color: #FFC107;
          transform: translateY(-1px);
        }

        .chat-input-form {
          padding: 1rem;
          border-top: 1px solid #dee2e6;
          background: white;
        }

        .chat-input-container {
          display: flex;
          gap: 0.5rem;
          align-items: center;
        }

        .chat-input {
          flex: 1;
          padding: 0.75rem 1rem;
          border: 1px solid #dee2e6;
          border-radius: 25px;
          font-size: 0.9rem;
          outline: none;
          transition: all 0.3s ease;
          background: #f8f9fa;
        }

        .chat-input:focus {
          border-color: #003366;
          background: white;
        }

        .chat-send-btn {
          width: 40px;
          height: 40px;
          background: #003366;
          color: white;
          border: none;
          border-radius: 50%;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;
        }

        .chat-send-btn:hover:not(:disabled) {
          background: #004080;
          transform: scale(1.05);
        }

        .chat-send-btn:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        @keyframes pulse {
          0%, 100% { box-shadow: 0 4px 20px rgba(255, 215, 0, 0.4); }
          50% { box-shadow: 0 4px 20px rgba(255, 215, 0, 0.8), 0 0 0 10px rgba(255, 215, 0, 0.2); }
        }

        @keyframes pulse-dot {
          0%, 100% { opacity: 1; }
          50% { opacity: 0.5; }
        }

        @keyframes typing {
          0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
          40% { transform: scale(1); opacity: 1; }
        }

        @media (max-width: 768px) {
          .static-chatbot-window {
            bottom: 0;
            ${isRTL ? 'left: 0;' : 'right: 0;'}
            width: 100vw;
            height: 100vh;
            border-radius: 0;
          }

          .static-chatbot-window.minimized {
            height: 60px;
            width: 350px;
            bottom: 20px;
            ${isRTL ? 'left: 20px;' : 'right: 20px;'}
            border-radius: 15px;
          }
        }
      `}</style>
    </div>
  );
};

export default StaticChatbot;
