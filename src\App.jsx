import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { LanguageProvider } from './contexts/LanguageContext';
import { CartProvider } from './contexts/CartContext';
import { FavoritesProvider } from './contexts/FavoritesContext';
import { ModalProvider } from './contexts/ModalContext';
import { NotificationProvider } from './contexts/NotificationContext';
import { ChatbotProvider } from './contexts/ChatbotContext';
import Navbar from './components/Navbar';
import Footer from './components/Footer';
import Cart from './components/Cart';
import Modal from './components/Modal';
import Notifications from './components/Notifications';
import ChatbotWidget from './components/ChatbotWidget';
import Home from './pages/Home';
import Products from './pages/Products';
import About from './pages/About';
import Timbres from './pages/Timbres';
import Favorites from './pages/Favorites';


function App() {
  return (
    <LanguageProvider>
      <NotificationProvider>
        <FavoritesProvider>
          <CartProvider>
            <ModalProvider>
              <ChatbotProvider>
                <Router>
                  <div className="App">
                    <Navbar />
                    <main className="main-content">
                      <Routes>
                        <Route path="/" element={<Home />} />
                        <Route path="/products" element={<Products />} />
                        <Route path="/categories" element={<Products />} />
                        <Route path="/new-releases" element={<Products />} />
                        <Route path="/about" element={<About />} />
                        <Route path="/contact" element={<About />} />
                        <Route path="/timbres" element={<Timbres />} />
                        <Route path="/favorites" element={<Favorites />} />
                      </Routes>
                    </main>
                    <Footer />
                    <Cart />
                    <Modal />
                    <Notifications />
                    <ChatbotWidget />
                  </div>
                </Router>
              </ChatbotProvider>
            </ModalProvider>
          </CartProvider>
        </FavoritesProvider>
      </NotificationProvider>
    </LanguageProvider>
  );
}

export default App;
