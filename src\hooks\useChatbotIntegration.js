import { useCallback } from 'react';

export const useChatbotIntegration = (timbres = []) => {
  
  // Function to handle when chatbot scrolls to a stamp
  const handleScrollToStamp = useCallback((stampId) => {
    console.log(`Chatbot scrolled to stamp: ${stampId}`);
    
    // You can add additional logic here, such as:
    // - Analytics tracking
    // - Highlighting the stamp
    // - Opening a modal with stamp details
    // - Adding to favorites
    
    // Example: Add a temporary highlight class
    const element = document.getElementById(`stamp-${stampId}`);
    if (element) {
      element.classList.add('chatbot-highlighted');
      setTimeout(() => {
        element.classList.remove('chatbot-highlighted');
      }, 3000);
    }
  }, []);

  // Function to prepare stamps data for chatbot
  const prepareChatbotData = useCallback(() => {
    return timbres.map(timbre => ({
      id: timbre.id,
      title: timbre.attributes?.title || '',
      description: timbre.attributes?.description || '',
      category: timbre.attributes?.category || '',
      // Add any other fields you want the chatbot to search through
    }));
  }, [timbres]);

  // Function to add IDs to stamp cards for scrolling
  const addStampIds = useCallback(() => {
    // This should be called after stamps are rendered
    setTimeout(() => {
      const stampCards = document.querySelectorAll('.stamp-card, .timbre-card');
      stampCards.forEach((card, index) => {
        if (timbres[index]) {
          card.id = `stamp-${timbres[index].id}`;
        }
      });
    }, 100);
  }, [timbres]);

  return {
    handleScrollToStamp,
    prepareChatbotData,
    addStampIds,
    chatbotData: prepareChatbotData()
  };
};
