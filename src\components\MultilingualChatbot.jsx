import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const MultilingualChatbot = ({ timbres = [], language = 'fr' }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [messages, setMessages] = useState([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef(null);

  // Multilingual content
  const content = {
    fr: {
      title: "Assistant Timbres",
      status: "En ligne",
      placeholder: "Tapez votre message...",
      welcome: "Bonjour ! Je suis votre assistant pour explorer notre collection de timbres tunisiens. Comment puis-je vous aider ?",
      quickActions: [
        { id: 'show-all', text: '🔍 Voir tous les timbres', action: 'show-all' },
        { id: 'about', text: 'ℹ️ À propos du site', action: 'about' },
        { id: 'order', text: '📦 Comment commander ?', action: 'order' },
        { id: 'contact', text: '📞 Contact', action: 'contact' }
      ],
      responses: {
        greeting: "Bonjour ! Bienvenue dans notre collection de timbres tunisiens. Que souhaitez-vous découvrir aujourd'hui ? 😊",
        showAll: `Voici notre collection complète ! Nous avons ${timbres.length} timbres magnifiques à découvrir. Faites défiler pour les explorer ! 📮`,
        about: "Ce site présente la collection officielle des timbres tunisiens. Vous pouvez explorer notre patrimoine philatélique, découvrir l'histoire de nos timbres et ajouter vos favoris ! 🇹🇳",
        order: "Notre site est un magazine philatélique pour découvrir et explorer les timbres tunisiens. Pour plus d'informations sur l'acquisition de timbres, contactez-nous directement.",
        contact: "Pour nous contacter : visitez la section Contact de notre site ou utilisez le formulaire en bas de page. Notre équipe sera ravie de vous aider ! 📞",
        thanks: "Je vous en prie ! N'hésitez pas si vous avez d'autres questions sur notre collection ! 🙏",
        found: "Parfait ! Je vous montre le timbre",
        foundSuffix: "Je l'ai mis en évidence pour vous ! 🎯",
        notFound: "Je n'ai pas trouvé de timbre correspondant à",
        notFoundSuffix: "Voulez-vous voir tous nos timbres disponibles ?",
        default: "Je peux vous aider à :\n• Trouver un timbre spécifique\n• Voir toute la collection\n• Obtenir des informations sur le site\n• Vous donner les contacts\n\nQue souhaitez-vous faire ? 🤔"
      }
    },
    ar: {
      title: "مساعد الطوابع",
      status: "متصل",
      placeholder: "اكتب رسالتك...",
      welcome: "مرحباً ! أنا مساعدكم لاستكشاف مجموعة الطوابع التونسية. كيف يمكنني مساعدتكم ؟",
      quickActions: [
        { id: 'show-all', text: '🔍 عرض جميع الطوابع', action: 'show-all' },
        { id: 'about', text: 'ℹ️ حول الموقع', action: 'about' },
        { id: 'order', text: '📦 كيفية الطلب ؟', action: 'order' },
        { id: 'contact', text: '📞 اتصال', action: 'contact' }
      ],
      responses: {
        greeting: "مرحباً ! أهلاً بكم في مجموعة الطوابع التونسية. ماذا تودون اكتشافه اليوم ؟ 😊",
        showAll: `إليكم مجموعتنا الكاملة ! لدينا ${timbres.length} طابعاً رائعاً لاكتشافه. تصفحوا لاستكشافها ! 📮`,
        about: "يعرض هذا الموقع المجموعة الرسمية للطوابع التونسية. يمكنكم استكشاف تراثنا الفيلاتيلي واكتشاف تاريخ طوابعنا وإضافة المفضلة ! 🇹🇳",
        order: "موقعنا هو مجلة فيلاتيلية لاكتشاف واستكشاف الطوابع التونسية. لمزيد من المعلومات حول الحصول على الطوابع، اتصلوا بنا مباشرة.",
        contact: "للاتصال بنا : زوروا قسم الاتصال في موقعنا أو استخدموا النموذج في أسفل الصفحة. فريقنا سيكون سعيداً لمساعدتكم ! 📞",
        thanks: "العفو ! لا تترددوا إذا كان لديكم أسئلة أخرى حول مجموعتنا ! 🙏",
        found: "ممتاز ! إليكم الطابع",
        foundSuffix: "لقد أبرزته لكم ! 🎯",
        notFound: "لم أجد طابعاً يطابق",
        notFoundSuffix: "هل تريدون رؤية جميع طوابعنا المتاحة ؟",
        default: "يمكنني مساعدتكم في :\n• العثور على طابع محدد\n• رؤية المجموعة كاملة\n• الحصول على معلومات الموقع\n• إعطاؤكم معلومات الاتصال\n\nماذا تودون فعله ؟ 🤔"
      }
    },
    en: {
      title: "Stamps Assistant",
      status: "Online",
      placeholder: "Type your message...",
      welcome: "Hello! I'm your assistant to explore our Tunisian stamps collection. How can I help you?",
      quickActions: [
        { id: 'show-all', text: '🔍 View all stamps', action: 'show-all' },
        { id: 'about', text: 'ℹ️ About the site', action: 'about' },
        { id: 'order', text: '📦 How to order?', action: 'order' },
        { id: 'contact', text: '📞 Contact', action: 'contact' }
      ],
      responses: {
        greeting: "Hello! Welcome to our Tunisian stamps collection. What would you like to discover today? 😊",
        showAll: `Here's our complete collection! We have ${timbres.length} magnificent stamps to discover. Scroll to explore them! 📮`,
        about: "This site showcases the official collection of Tunisian stamps. You can explore our philatelic heritage, discover the history of our stamps and add favorites! 🇹🇳",
        order: "Our site is a philatelic magazine to discover and explore Tunisian stamps. For more information about acquiring stamps, contact us directly.",
        contact: "To contact us: visit the Contact section of our site or use the form at the bottom of the page. Our team will be happy to help you! 📞",
        thanks: "You're welcome! Don't hesitate if you have other questions about our collection! 🙏",
        found: "Perfect! Here's the stamp",
        foundSuffix: "I've highlighted it for you! 🎯",
        notFound: "I couldn't find a stamp matching",
        notFoundSuffix: "Would you like to see all our available stamps?",
        default: "I can help you:\n• Find a specific stamp\n• View the entire collection\n• Get site information\n• Give you contact details\n\nWhat would you like to do? 🤔"
      }
    }
  };

  const currentContent = content[language] || content.fr;
  const isRTL = language === 'ar';

  // Initialize with welcome message
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      setTimeout(() => {
        addBotMessage(currentContent.welcome);
      }, 500);
    }
  }, [isOpen, messages.length, currentContent.welcome]);

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, isTyping]);

  const addBotMessage = (text) => {
    setMessages(prev => [...prev, {
      id: Date.now(),
      text,
      sender: 'bot',
      timestamp: new Date()
    }]);
  };

  const addUserMessage = (text) => {
    setMessages(prev => [...prev, {
      id: Date.now(),
      text,
      sender: 'user',
      timestamp: new Date()
    }]);
  };

  const findStampByName = (searchTerm) => {
    const term = searchTerm.toLowerCase();
    return timbres.find(timbre => {
      const title = timbre.attributes?.title?.toLowerCase() || '';
      const description = timbre.attributes?.description?.toLowerCase() || '';
      const category = timbre.attributes?.category?.toLowerCase() || '';
      return title.includes(term) || description.includes(term) || category.includes(term);
    });
  };

  const scrollToStamp = (stampId) => {
    const element = document.getElementById(`stamp-${stampId}`);
    if (element) {
      element.scrollIntoView({ 
        behavior: 'smooth', 
        block: 'center' 
      });
      
      // Highlight animation
      element.style.transition = 'all 0.5s ease';
      element.style.transform = 'scale(1.05)';
      element.style.boxShadow = '0 0 30px rgba(255, 215, 0, 0.8)';
      element.style.border = '3px solid #FFD700';
      element.style.borderRadius = '15px';
      
      setTimeout(() => {
        element.style.transform = '';
        element.style.boxShadow = '';
        element.style.border = '';
      }, 3000);
    }
  };

  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const processMessage = (message) => {
    const lowerMessage = message.toLowerCase();
    
    // Multilingual keyword detection
    const stampKeywords = {
      fr: ['timbre', 'voir', 'cherche', 'trouve', 'patrimoine', 'tunisien', 'nature', 'carthage', 'indépendance', 'sport'],
      ar: ['طابع', 'أريد', 'أبحث', 'تراث', 'تونسي', 'طبيعة', 'قرطاج', 'استقلال', 'رياضة'],
      en: ['stamp', 'show', 'find', 'search', 'heritage', 'tunisian', 'nature', 'carthage', 'independence', 'sport']
    };

    const showAllKeywords = {
      fr: ['tous', 'collection', 'montrer', 'afficher'],
      ar: ['جميع', 'كل', 'مجموعة', 'أظهر'],
      en: ['all', 'collection', 'show', 'display']
    };

    const greetingKeywords = {
      fr: ['bonjour', 'salut', 'bonsoir'],
      ar: ['مرحبا', 'أهلا', 'السلام'],
      en: ['hello', 'hi', 'hey', 'good']
    };

    const thanksKeywords = {
      fr: ['merci', 'remercie'],
      ar: ['شكرا', 'شكراً'],
      en: ['thank', 'thanks']
    };

    // Check for stamp search
    const currentStampKeywords = stampKeywords[language] || stampKeywords.fr;
    const foundStampKeyword = currentStampKeywords.find(keyword => lowerMessage.includes(keyword));
    
    if (foundStampKeyword) {
      const foundStamp = findStampByName(foundStampKeyword);
      if (foundStamp) {
        scrollToStamp(foundStamp.id);
        return `${currentContent.responses.found} "${foundStamp.attributes?.title}". ${currentContent.responses.foundSuffix}`;
      } else {
        return `${currentContent.responses.notFound} "${foundStampKeyword}". ${currentContent.responses.notFoundSuffix}`;
      }
    }

    // Check for show all
    const currentShowAllKeywords = showAllKeywords[language] || showAllKeywords.fr;
    if (currentShowAllKeywords.some(keyword => lowerMessage.includes(keyword))) {
      scrollToSection('timbres-section');
      return currentContent.responses.showAll;
    }

    // Check for greetings
    const currentGreetingKeywords = greetingKeywords[language] || greetingKeywords.fr;
    if (currentGreetingKeywords.some(keyword => lowerMessage.includes(keyword))) {
      return currentContent.responses.greeting;
    }

    // Check for thanks
    const currentThanksKeywords = thanksKeywords[language] || thanksKeywords.fr;
    if (currentThanksKeywords.some(keyword => lowerMessage.includes(keyword))) {
      return currentContent.responses.thanks;
    }

    // Default response
    return currentContent.responses.default;
  };

  const handleSendMessage = () => {
    if (!inputValue.trim()) return;

    addUserMessage(inputValue);
    setIsTyping(true);

    setTimeout(() => {
      const response = processMessage(inputValue);
      addBotMessage(response);
      setIsTyping(false);
    }, 1000 + Math.random() * 1000);

    setInputValue('');
  };

  const handleQuickAction = (action) => {
    let response = '';
    let userMessage = '';

    switch (action) {
      case 'show-all':
        userMessage = currentContent.quickActions.find(a => a.action === 'show-all')?.text || '';
        scrollToSection('timbres-section');
        response = currentContent.responses.showAll;
        break;
      case 'about':
        userMessage = currentContent.quickActions.find(a => a.action === 'about')?.text || '';
        response = currentContent.responses.about;
        break;
      case 'order':
        userMessage = currentContent.quickActions.find(a => a.action === 'order')?.text || '';
        response = currentContent.responses.order;
        break;
      case 'contact':
        userMessage = currentContent.quickActions.find(a => a.action === 'contact')?.text || '';
        response = currentContent.responses.contact;
        break;
      default:
        response = currentContent.responses.default;
    }

    if (userMessage) addUserMessage(userMessage);
    setTimeout(() => addBotMessage(response), 500);
  };

  // Chat button
  if (!isOpen) {
    return (
      <motion.button
        onClick={() => setIsOpen(true)}
        className="chatbot-button"
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.95 }}
        animate={{ 
          boxShadow: [
            '0 4px 20px rgba(255, 215, 0, 0.4)',
            '0 4px 30px rgba(255, 215, 0, 0.8)',
            '0 4px 20px rgba(255, 215, 0, 0.4)'
          ]
        }}
        transition={{ 
          boxShadow: { duration: 2, repeat: Infinity },
          scale: { duration: 0.2 }
        }}
        style={{
          position: 'fixed',
          bottom: '20px',
          right: isRTL ? 'auto' : '20px',
          left: isRTL ? '20px' : 'auto',
          width: '60px',
          height: '60px',
          backgroundColor: '#FFD700',
          color: '#003366',
          border: '3px solid #003366',
          borderRadius: '50%',
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          fontSize: '24px',
          zIndex: 9999,
          boxShadow: '0 4px 20px rgba(255, 215, 0, 0.4)'
        }}
      >
        💬
      </motion.button>
    );
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, scale: 0.8, y: 100 }}
        animate={{ 
          opacity: 1, 
          scale: 1, 
          y: 0,
          height: isMinimized ? '60px' : '500px'
        }}
        exit={{ opacity: 0, scale: 0.8, y: 100 }}
        transition={{ duration: 0.3, ease: 'easeOut' }}
        className="chatbot-window"
        style={{
          position: 'fixed',
          bottom: '20px',
          right: isRTL ? 'auto' : '20px',
          left: isRTL ? '20px' : 'auto',
          width: '350px',
          backgroundColor: 'white',
          borderRadius: '15px',
          boxShadow: '0 10px 40px rgba(0, 51, 102, 0.2)',
          display: 'flex',
          flexDirection: 'column',
          zIndex: 9999,
          overflow: 'hidden',
          direction: isRTL ? 'rtl' : 'ltr'
        }}
      >
        {/* Header */}
        <div style={{
          background: 'linear-gradient(135deg, #003366 0%, #004080 100%)',
          color: 'white',
          padding: '1rem',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
            <div style={{
              width: '35px',
              height: '35px',
              backgroundColor: '#FFD700',
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '1.1rem'
            }}>
              🤖
            </div>
            <div>
              <h3 style={{ margin: 0, fontSize: '0.95rem' }}>{currentContent.title}</h3>
              <span style={{ fontSize: '0.75rem', opacity: 0.9 }}>
                <span style={{
                  width: '6px',
                  height: '6px',
                  backgroundColor: '#4CAF50',
                  borderRadius: '50%',
                  display: 'inline-block',
                  marginRight: '0.25rem'
                }}></span>
                {currentContent.status}
              </span>
            </div>
          </div>
          <div style={{ display: 'flex', gap: '0.5rem' }}>
            <button
              onClick={() => setIsMinimized(!isMinimized)}
              style={{
                background: 'transparent',
                border: 'none',
                color: 'white',
                cursor: 'pointer',
                padding: '0.25rem',
                borderRadius: '4px',
                fontSize: '16px'
              }}
            >
              {isMinimized ? '□' : '−'}
            </button>
            <button
              onClick={() => setIsOpen(false)}
              style={{
                background: 'transparent',
                border: 'none',
                color: 'white',
                cursor: 'pointer',
                padding: '0.25rem',
                borderRadius: '4px',
                fontSize: '16px'
              }}
            >
              ✕
            </button>
          </div>
        </div>

        {!isMinimized && (
          <>
            {/* Messages */}
            <div style={{
              flex: 1,
              padding: '1rem',
              overflowY: 'auto',
              display: 'flex',
              flexDirection: 'column',
              gap: '0.75rem',
              maxHeight: '300px'
            }}>
              <AnimatePresence>
                {messages.map((message) => (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    style={{
                      display: 'flex',
                      justifyContent: message.sender === 'bot' ? 'flex-start' : 'flex-end',
                      marginBottom: '0.5rem'
                    }}
                  >
                    <div style={{
                      maxWidth: '80%',
                      padding: '0.75rem 1rem',
                      borderRadius: '18px',
                      backgroundColor: message.sender === 'bot' ? '#f8f9fa' : '#003366',
                      color: message.sender === 'bot' ? '#343a40' : 'white',
                      borderBottomLeftRadius: message.sender === 'bot' ? '4px' : '18px',
                      borderBottomRightRadius: message.sender === 'bot' ? '18px' : '4px',
                      wordWrap: 'break-word'
                    }}>
                      <p style={{ margin: 0, fontSize: '0.9rem', lineHeight: 1.4, whiteSpace: 'pre-line' }}>
                        {message.text}
                      </p>
                      <span style={{
                        fontSize: '0.7rem',
                        opacity: 0.7,
                        display: 'block',
                        marginTop: '0.25rem',
                        textAlign: message.sender === 'bot' ? (isRTL ? 'right' : 'left') : (isRTL ? 'left' : 'right')
                      }}>
                        {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </span>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>
              
              {isTyping && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  style={{ display: 'flex', justifyContent: 'flex-start' }}
                >
                  <div style={{
                    backgroundColor: '#f8f9fa',
                    borderRadius: '18px',
                    padding: '0.75rem 1rem',
                    maxWidth: '60px'
                  }}>
                    <div style={{ display: 'flex', gap: '4px', alignItems: 'center' }}>
                      {[0, 1, 2].map(i => (
                        <motion.span
                          key={i}
                          animate={{ scale: [0.8, 1, 0.8] }}
                          transition={{ 
                            duration: 1.4, 
                            repeat: Infinity, 
                            delay: i * 0.16 
                          }}
                          style={{ 
                            width: '6px', 
                            height: '6px', 
                            backgroundColor: '#6c757d', 
                            borderRadius: '50%',
                            display: 'block'
                          }}
                        />
                      ))}
                    </div>
                  </div>
                </motion.div>
              )}
              
              <div ref={messagesEndRef} />
            </div>

            {/* Quick Actions */}
            {messages.length <= 1 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5 }}
                style={{
                  padding: '1rem',
                  borderTop: '1px solid #dee2e6',
                  backgroundColor: '#f8f9fa'
                }}
              >
                <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '0.5rem' }}>
                  {currentContent.quickActions.map((action) => (
                    <motion.button
                      key={action.id}
                      onClick={() => handleQuickAction(action.action)}
                      whileHover={{ scale: 1.02, backgroundColor: '#FFD700' }}
                      whileTap={{ scale: 0.98 }}
                      style={{
                        padding: '0.75rem',
                        backgroundColor: 'white',
                        border: '1px solid #dee2e6',
                        borderRadius: '8px',
                        fontSize: '0.85rem',
                        color: '#003366',
                        cursor: 'pointer',
                        textAlign: isRTL ? 'right' : 'left',
                        transition: 'all 0.3s ease'
                      }}
                    >
                      {action.text}
                    </motion.button>
                  ))}
                </div>
              </motion.div>
            )}

            {/* Input */}
            <div style={{
              padding: '1rem',
              borderTop: '1px solid #dee2e6',
              backgroundColor: 'white'
            }}>
              <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'center' }}>
                <input
                  type="text"
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                  placeholder={currentContent.placeholder}
                  style={{
                    flex: 1,
                    padding: '0.75rem 1rem',
                    border: '1px solid #dee2e6',
                    borderRadius: '25px',
                    fontSize: '0.9rem',
                    outline: 'none',
                    backgroundColor: '#f8f9fa',
                    direction: isRTL ? 'rtl' : 'ltr'
                  }}
                  disabled={isTyping}
                />
                <motion.button
                  onClick={handleSendMessage}
                  disabled={!inputValue.trim() || isTyping}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  style={{
                    width: '40px',
                    height: '40px',
                    backgroundColor: '#003366',
                    color: 'white',
                    border: 'none',
                    borderRadius: '50%',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '16px',
                    opacity: (!inputValue.trim() || isTyping) ? 0.5 : 1
                  }}
                >
                  {isRTL ? '←' : '→'}
                </motion.button>
              </div>
            </div>
          </>
        )}
      </motion.div>
    </AnimatePresence>
  );
};

export default MultilingualChatbot;
