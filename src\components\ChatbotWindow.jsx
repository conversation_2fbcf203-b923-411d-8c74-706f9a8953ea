import React, { useRef, useEffect } from 'react';
import { X, Minus, Trash2 } from 'lucide-react';
import { useChatbot } from '../contexts/ChatbotContext';
import { useLanguage } from '../contexts/LanguageContext';
import MessageBubble from './MessageBubble';
import ChatInput from './ChatInput';
import QuickActions from './QuickActions';

const ChatbotWindow = () => {
  const { 
    messages, 
    isTyping, 
    closeChatbot, 
    clearMessages 
  } = useChatbot();
  const { t, isRTL } = useLanguage();
  const messagesEndRef = useRef(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, isTyping]);

  return (
    <div className="chatbot-window">
      {/* Header */}
      <div className="chatbot-header">
        <div className="chatbot-title">
          <div className="chatbot-avatar">🤖</div>
          <div>
            <h3>{t('chatbot.title')}</h3>
            <span className="chatbot-status">
              <span className="status-dot"></span>
              En ligne
            </span>
          </div>
        </div>
        <div className="chatbot-actions">
          <button
            onClick={clearMessages}
            className="chatbot-action-btn"
            title={t('chatbot.clear')}
          >
            <Trash2 size={16} />
          </button>
          <button
            onClick={closeChatbot}
            className="chatbot-action-btn"
            title={t('chatbot.close')}
          >
            <X size={16} />
          </button>
        </div>
      </div>

      {/* Messages */}
      <div className="chatbot-messages">
        {messages.map((message) => (
          <MessageBubble key={message.id} message={message} />
        ))}
        
        {/* Typing indicator */}
        {isTyping && (
          <div className="typing-indicator">
            <div className="typing-bubble">
              <div className="typing-dots">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Quick Actions */}
      {messages.length <= 1 && <QuickActions />}

      {/* Input */}
      <ChatInput />

      <style>{`
        .chatbot-window {
          position: fixed;
          bottom: 20px;
          ${isRTL ? 'left: 20px;' : 'right: 20px;'}
          width: 350px;
          height: 500px;
          background: var(--white);
          border-radius: 15px;
          box-shadow: var(--shadow-hover);
          display: flex;
          flex-direction: column;
          z-index: 1001;
          overflow: hidden;
        }

        .chatbot-header {
          background: var(--primary-blue);
          color: var(--white);
          padding: 1rem;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .chatbot-title {
          display: flex;
          align-items: center;
          gap: 0.75rem;
        }

        .chatbot-avatar {
          width: 40px;
          height: 40px;
          background: var(--secondary-gold);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 1.2rem;
        }

        .chatbot-title h3 {
          margin: 0;
          font-size: 1rem;
          color: var(--white);
        }

        .chatbot-status {
          font-size: 0.8rem;
          opacity: 0.8;
          display: flex;
          align-items: center;
          gap: 0.25rem;
        }

        .status-dot {
          width: 6px;
          height: 6px;
          background: #4CAF50;
          border-radius: 50%;
          animation: pulse 2s infinite;
        }

        @keyframes pulse {
          0% {
            opacity: 1;
          }
          50% {
            opacity: 0.5;
          }
          100% {
            opacity: 1;
          }
        }

        .chatbot-actions {
          display: flex;
          gap: 0.5rem;
        }

        .chatbot-action-btn {
          background: transparent;
          border: none;
          color: var(--white);
          cursor: pointer;
          padding: 0.25rem;
          border-radius: 4px;
          transition: var(--transition);
        }

        .chatbot-action-btn:hover {
          background: rgba(255, 255, 255, 0.1);
        }

        .chatbot-messages {
          flex: 1;
          padding: 1rem;
          overflow-y: auto;
          display: flex;
          flex-direction: column;
          gap: 0.75rem;
        }

        .chatbot-messages::-webkit-scrollbar {
          width: 4px;
        }

        .chatbot-messages::-webkit-scrollbar-track {
          background: var(--light-gray);
        }

        .chatbot-messages::-webkit-scrollbar-thumb {
          background: var(--medium-gray);
          border-radius: 2px;
        }

        .typing-indicator {
          display: flex;
          justify-content: flex-start;
        }

        .typing-bubble {
          background: var(--light-gray);
          border-radius: 18px;
          padding: 0.75rem 1rem;
          max-width: 60px;
        }

        .typing-dots {
          display: flex;
          gap: 4px;
          align-items: center;
        }

        .typing-dots span {
          width: 6px;
          height: 6px;
          background: var(--medium-gray);
          border-radius: 50%;
          animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dots span:nth-child(1) {
          animation-delay: -0.32s;
        }

        .typing-dots span:nth-child(2) {
          animation-delay: -0.16s;
        }

        @keyframes typing {
          0%, 80%, 100% {
            transform: scale(0.8);
            opacity: 0.5;
          }
          40% {
            transform: scale(1);
            opacity: 1;
          }
        }

        @media (max-width: 768px) {
          .chatbot-window {
            bottom: 0;
            ${isRTL ? 'left: 0;' : 'right: 0;'}
            width: 100vw;
            height: 100vh;
            border-radius: 0;
          }
        }

        @media (max-width: 480px) {
          .chatbot-window {
            width: 100vw;
            height: 100vh;
          }
        }
      `}</style>
    </div>
  );
};

export default ChatbotWindow;
