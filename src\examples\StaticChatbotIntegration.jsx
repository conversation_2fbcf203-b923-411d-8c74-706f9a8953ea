// INTEGRATION EXAMPLE FOR STATIC STAMP CHATBOT
// ============================================

import React, { useState, useEffect } from 'react';
import StaticStampChatbot from '../components/StaticStampChatbot';

// Example HomePage component showing how to integrate the chatbot
const HomePage = () => {
  const [timbres, setTimbres] = useState([]);

  // Example timbres data (replace with your actual data fetching)
  useEffect(() => {
    // This would be your actual API call
    const exampleTimbres = [
      {
        id: 1,
        attributes: {
          nom: "Révolution du Jasmin",
          prix: 1.5,
          description: "Timbre commémoratif de la révolution tunisienne"
        }
      },
      {
        id: 2,
        attributes: {
          nom: "Patrimoine Tunisien",
          prix: 2.0,
          description: "Collection patrimoine architectural"
        }
      },
      {
        id: 3,
        attributes: {
          nom: "Faune Marine",
          prix: 1.8,
          description: "Faune marine des côtes tunisiennes"
        }
      }
    ];
    setTimbres(exampleTimbres);
  }, []);

  return (
    <div className="homepage">
      {/* Your existing content */}
      <section className="hero">
        <h1>Collection de Timbres Tunisiens</h1>
      </section>

      {/* Stamps grid - IMPORTANT: Each card must have id="timbre-{id}" */}
      <section className="stamps-section">
        <div className="stamps-grid">
          {timbres.map((timbre) => (
            <div 
              key={timbre.id} 
              id={`timbre-${timbre.id}`}  // ← REQUIRED ID FORMAT
              className="stamp-card"
            >
              <h3>{timbre.attributes.nom}</h3>
              <p>{timbre.attributes.description}</p>
              <span className="price">{timbre.attributes.prix}€</span>
            </div>
          ))}
        </div>
      </section>

      {/* ADD THE CHATBOT COMPONENT */}
      <StaticStampChatbot timbres={timbres} />

      <style>{`
        .homepage {
          padding: 2rem;
        }

        .hero {
          text-align: center;
          margin-bottom: 3rem;
        }

        .stamps-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
          gap: 2rem;
          margin-bottom: 2rem;
        }

        .stamp-card {
          border: 2px solid #e0e0e0;
          border-radius: 10px;
          padding: 1.5rem;
          background: white;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          transition: all 0.3s ease;
        }

        .stamp-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 5px 20px rgba(0,0,0,0.15);
        }

        .stamp-card h3 {
          margin: 0 0 1rem 0;
          color: #003366;
          font-size: 1.2rem;
        }

        .stamp-card p {
          margin: 0 0 1rem 0;
          color: #666;
          line-height: 1.4;
        }

        .price {
          font-weight: bold;
          color: #FFD700;
          background: #003366;
          padding: 0.5rem 1rem;
          border-radius: 20px;
          font-size: 1.1rem;
        }

        /* The highlight class is automatically applied by the chatbot */
        .highlight {
          border: 3px solid #FFD700 !important;
          box-shadow: 0 0 20px rgba(255, 215, 0, 0.8) !important;
          transition: all 0.3s ease !important;
        }

        @media (max-width: 768px) {
          .stamps-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
          }
          
          .homepage {
            padding: 1rem;
          }
        }
      `}</style>
    </div>
  );
};

export default HomePage;

/*
INTEGRATION CHECKLIST:
======================

✅ 1. Import the component:
   import StaticStampChatbot from '../components/StaticStampChatbot';

✅ 2. Add the component to your page:
   <StaticStampChatbot timbres={timbres} />

✅ 3. Ensure each stamp card has the correct ID:
   id={`timbre-${timbre.id}`}

✅ 4. Make sure your timbres data structure matches:
   {
     id: number,
     attributes: {
       nom: string,  // ← This is the field used for matching
       prix: number,
       description: string
     }
   }

✅ 5. The highlight CSS class is included in the component

HOW IT WORKS:
=============

1. User clicks the 💬 button → Chat opens
2. User types exact stamp name (e.g., "Révolution du Jasmin")
3. Chatbot searches for exact match (case insensitive)
4. If found:
   - Scrolls to element with id="timbre-{id}"
   - Adds "highlight" class for 3 seconds
   - Shows confirmation message
5. If not found:
   - Shows default help message

TESTING:
========

Try typing these exact names in the chatbot:
- "Révolution du Jasmin"
- "Patrimoine Tunisien" 
- "Faune Marine"
- "Random Name" (should show help message)

CUSTOMIZATION:
==============

You can customize:
- Colors in the CSS (Tunisia Post theme: #003366, #FFD700)
- Help message text in the processMessage function
- Highlight duration (currently 3 seconds)
- Chat window size and position
*/
