import React, { useState, useRef, useEffect } from 'react';

const SimpleChatbot = ({ timbres = [] }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef(null);

  // Initialize with welcome message
  useEffect(() => {
    if (isOpen && messages.length === 0) {
      setTimeout(() => {
        addBotMessage("Bonjour ! Je suis votre assistant pour explorer notre collection de timbres tunisiens. Comment puis-je vous aider ?");
      }, 500);
    }
  }, [isOpen, messages.length]);

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, isTyping]);

  const addBotMessage = (text) => {
    setMessages(prev => [...prev, {
      id: Date.now(),
      text,
      sender: 'bot',
      timestamp: new Date()
    }]);
  };

  const addUserMessage = (text) => {
    setMessages(prev => [...prev, {
      id: Date.now(),
      text,
      sender: 'user',
      timestamp: new Date()
    }]);
  };

  const findStampByName = (searchTerm) => {
    const term = searchTerm.toLowerCase();
    return timbres.find(timbre => {
      const title = timbre.attributes?.title?.toLowerCase() || '';
      const description = timbre.attributes?.description?.toLowerCase() || '';
      return title.includes(term) || description.includes(term);
    });
  };

  const scrollToStamp = (stampId) => {
    const element = document.getElementById(`stamp-${stampId}`);
    if (element) {
      element.scrollIntoView({ 
        behavior: 'smooth', 
        block: 'center' 
      });
      // Highlight effect
      element.style.boxShadow = '0 0 20px rgba(255, 215, 0, 0.8)';
      element.style.transform = 'scale(1.05)';
      element.style.transition = 'all 0.3s ease';
      setTimeout(() => {
        element.style.boxShadow = '';
        element.style.transform = '';
      }, 2000);
    }
  };

  const processMessage = (message) => {
    const lowerMessage = message.toLowerCase();
    
    // Search for specific stamps
    if (lowerMessage.includes('timbre') || lowerMessage.includes('voir') || lowerMessage.includes('cherche')) {
      const stampKeywords = ['patrimoine', 'tunisien', 'nature', 'carthage', 'indépendance', 'sport'];
      const foundKeyword = stampKeywords.find(keyword => lowerMessage.includes(keyword));
      
      if (foundKeyword) {
        const foundStamp = findStampByName(foundKeyword);
        if (foundStamp) {
          scrollToStamp(foundStamp.id);
          return `Parfait ! Je vous montre le timbre "${foundStamp.attributes?.title}". Je l'ai mis en évidence pour vous ! 🎯`;
        } else {
          return `Je n'ai pas trouvé de timbre correspondant à "${foundKeyword}". Voulez-vous voir tous nos timbres disponibles ?`;
        }
      }
    }

    // Show all stamps
    if (lowerMessage.includes('tous') || lowerMessage.includes('all') || lowerMessage.includes('collection')) {
      const timbresSection = document.getElementById('timbres-section');
      if (timbresSection) {
        timbresSection.scrollIntoView({ behavior: 'smooth' });
      }
      return `Voici notre collection complète ! Nous avons ${timbres.length} timbres magnifiques à découvrir. Faites défiler pour les explorer ! 📮`;
    }

    // About the site
    if (lowerMessage.includes('site') || lowerMessage.includes('about') || lowerMessage.includes('propos')) {
      return "Ce site présente la collection officielle des timbres tunisiens. Vous pouvez explorer notre patrimoine philatélique, découvrir l'histoire de nos timbres et ajouter vos favoris ! 🇹🇳";
    }

    // Contact info
    if (lowerMessage.includes('contact') || lowerMessage.includes('aide') || lowerMessage.includes('help')) {
      return "Pour nous contacter : visitez la section Contact de notre site ou utilisez le formulaire en bas de page. Notre équipe sera ravie de vous aider ! 📞";
    }

    // Greetings
    if (lowerMessage.includes('bonjour') || lowerMessage.includes('salut') || lowerMessage.includes('hello')) {
      return "Bonjour ! Bienvenue dans notre collection de timbres tunisiens. Que souhaitez-vous découvrir aujourd'hui ? 😊";
    }

    // Thanks
    if (lowerMessage.includes('merci') || lowerMessage.includes('thank')) {
      return "Je vous en prie ! N'hésitez pas si vous avez d'autres questions sur notre collection ! 🙏";
    }

    // Default response
    return "Je peux vous aider à : \n• Trouver un timbre spécifique \n• Voir toute la collection \n• Obtenir des informations sur le site \n• Vous donner les contacts \n\nQue souhaitez-vous faire ? 🤔";
  };

  const handleSendMessage = () => {
    if (!inputValue.trim()) return;

    addUserMessage(inputValue);
    setIsTyping(true);

    setTimeout(() => {
      const response = processMessage(inputValue);
      addBotMessage(response);
      setIsTyping(false);
    }, 1000 + Math.random() * 1000);

    setInputValue('');
  };

  const handleQuickAction = (action) => {
    switch (action) {
      case 'show-all':
        addUserMessage("Montrez-moi tous les timbres");
        setTimeout(() => {
          const response = processMessage("tous les timbres");
          addBotMessage(response);
        }, 500);
        break;
      case 'about':
        addUserMessage("Qu'est-ce que ce site ?");
        setTimeout(() => {
          addBotMessage("Ce site présente la collection officielle des timbres tunisiens. Vous pouvez explorer notre patrimoine philatélique, découvrir l'histoire de nos timbres et ajouter vos favoris ! 🇹🇳");
        }, 500);
        break;
      case 'contact':
        addUserMessage("Informations de contact");
        setTimeout(() => {
          addBotMessage("Pour nous contacter : visitez la section Contact de notre site ou utilisez le formulaire en bas de page. Notre équipe sera ravie de vous aider ! 📞");
        }, 500);
        break;
    }
  };

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        style={{
          position: 'fixed',
          bottom: '20px',
          right: '20px',
          width: '60px',
          height: '60px',
          backgroundColor: '#FFD700',
          color: '#003366',
          border: '3px solid #003366',
          borderRadius: '50%',
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          boxShadow: '0 4px 20px rgba(255, 215, 0, 0.4)',
          zIndex: 9999,
          fontSize: '24px',
          transition: 'all 0.3s ease'
        }}
        onMouseEnter={(e) => {
          e.target.style.transform = 'scale(1.1)';
          e.target.style.boxShadow = '0 6px 25px rgba(255, 215, 0, 0.6)';
        }}
        onMouseLeave={(e) => {
          e.target.style.transform = 'scale(1)';
          e.target.style.boxShadow = '0 4px 20px rgba(255, 215, 0, 0.4)';
        }}
      >
        💬
      </button>
    );
  }

  return (
    <div style={{
      position: 'fixed',
      bottom: '20px',
      right: '20px',
      width: '350px',
      height: '500px',
      backgroundColor: 'white',
      borderRadius: '15px',
      boxShadow: '0 10px 40px rgba(0, 51, 102, 0.2)',
      display: 'flex',
      flexDirection: 'column',
      zIndex: 9999,
      overflow: 'hidden'
    }}>
      {/* Header */}
      <div style={{
        background: 'linear-gradient(135deg, #003366 0%, #004080 100%)',
        color: 'white',
        padding: '1rem',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
          <div style={{
            width: '35px',
            height: '35px',
            backgroundColor: '#FFD700',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '1.1rem'
          }}>
            🤖
          </div>
          <div>
            <h3 style={{ margin: 0, fontSize: '0.95rem' }}>Assistant Timbres</h3>
            <span style={{ fontSize: '0.75rem', opacity: 0.9 }}>En ligne</span>
          </div>
        </div>
        <button
          onClick={() => setIsOpen(false)}
          style={{
            background: 'transparent',
            border: 'none',
            color: 'white',
            cursor: 'pointer',
            padding: '0.25rem',
            borderRadius: '4px',
            fontSize: '18px'
          }}
        >
          ✕
        </button>
      </div>

      {/* Messages */}
      <div style={{
        flex: 1,
        padding: '1rem',
        overflowY: 'auto',
        display: 'flex',
        flexDirection: 'column',
        gap: '0.75rem'
      }}>
        {messages.map((message) => (
          <div key={message.id} style={{
            display: 'flex',
            justifyContent: message.sender === 'bot' ? 'flex-start' : 'flex-end',
            marginBottom: '0.5rem'
          }}>
            <div style={{
              maxWidth: '80%',
              padding: '0.75rem 1rem',
              borderRadius: '18px',
              backgroundColor: message.sender === 'bot' ? '#f8f9fa' : '#003366',
              color: message.sender === 'bot' ? '#343a40' : 'white',
              borderBottomLeftRadius: message.sender === 'bot' ? '4px' : '18px',
              borderBottomRightRadius: message.sender === 'bot' ? '18px' : '4px',
              wordWrap: 'break-word'
            }}>
              <p style={{ margin: 0, fontSize: '0.9rem', lineHeight: 1.4, whiteSpace: 'pre-line' }}>
                {message.text}
              </p>
              <span style={{
                fontSize: '0.7rem',
                opacity: 0.7,
                display: 'block',
                marginTop: '0.25rem',
                textAlign: message.sender === 'bot' ? 'left' : 'right'
              }}>
                {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </span>
            </div>
          </div>
        ))}
        
        {isTyping && (
          <div style={{ display: 'flex', justifyContent: 'flex-start' }}>
            <div style={{
              backgroundColor: '#f8f9fa',
              borderRadius: '18px',
              padding: '0.75rem 1rem',
              maxWidth: '60px'
            }}>
              <div style={{ display: 'flex', gap: '4px', alignItems: 'center' }}>
                <span style={{ 
                  width: '6px', 
                  height: '6px', 
                  backgroundColor: '#6c757d', 
                  borderRadius: '50%',
                  animation: 'typing 1.4s infinite ease-in-out'
                }}></span>
                <span style={{ 
                  width: '6px', 
                  height: '6px', 
                  backgroundColor: '#6c757d', 
                  borderRadius: '50%',
                  animation: 'typing 1.4s infinite ease-in-out',
                  animationDelay: '-0.16s'
                }}></span>
                <span style={{ 
                  width: '6px', 
                  height: '6px', 
                  backgroundColor: '#6c757d', 
                  borderRadius: '50%',
                  animation: 'typing 1.4s infinite ease-in-out',
                  animationDelay: '-0.32s'
                }}></span>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Quick Actions */}
      {messages.length <= 1 && (
        <div style={{
          padding: '1rem',
          borderTop: '1px solid #dee2e6',
          backgroundColor: '#f8f9fa'
        }}>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr', gap: '0.5rem' }}>
            <button
              onClick={() => handleQuickAction('show-all')}
              style={{
                padding: '0.75rem',
                backgroundColor: 'white',
                border: '1px solid #dee2e6',
                borderRadius: '8px',
                fontSize: '0.85rem',
                color: '#003366',
                cursor: 'pointer',
                transition: 'all 0.3s ease'
              }}
              onMouseEnter={(e) => {
                e.target.style.backgroundColor = '#FFD700';
                e.target.style.borderColor = '#FFC107';
                e.target.style.transform = 'translateY(-1px)';
              }}
              onMouseLeave={(e) => {
                e.target.style.backgroundColor = 'white';
                e.target.style.borderColor = '#dee2e6';
                e.target.style.transform = 'translateY(0)';
              }}
            >
              🔍 Voir tous les timbres
            </button>
            <button
              onClick={() => handleQuickAction('about')}
              style={{
                padding: '0.75rem',
                backgroundColor: 'white',
                border: '1px solid #dee2e6',
                borderRadius: '8px',
                fontSize: '0.85rem',
                color: '#003366',
                cursor: 'pointer',
                transition: 'all 0.3s ease'
              }}
              onMouseEnter={(e) => {
                e.target.style.backgroundColor = '#FFD700';
                e.target.style.borderColor = '#FFC107';
                e.target.style.transform = 'translateY(-1px)';
              }}
              onMouseLeave={(e) => {
                e.target.style.backgroundColor = 'white';
                e.target.style.borderColor = '#dee2e6';
                e.target.style.transform = 'translateY(0)';
              }}
            >
              ℹ️ À propos du site
            </button>
            <button
              onClick={() => handleQuickAction('contact')}
              style={{
                padding: '0.75rem',
                backgroundColor: 'white',
                border: '1px solid #dee2e6',
                borderRadius: '8px',
                fontSize: '0.85rem',
                color: '#003366',
                cursor: 'pointer',
                transition: 'all 0.3s ease'
              }}
              onMouseEnter={(e) => {
                e.target.style.backgroundColor = '#FFD700';
                e.target.style.borderColor = '#FFC107';
                e.target.style.transform = 'translateY(-1px)';
              }}
              onMouseLeave={(e) => {
                e.target.style.backgroundColor = 'white';
                e.target.style.borderColor = '#dee2e6';
                e.target.style.transform = 'translateY(0)';
              }}
            >
              📞 Contact
            </button>
          </div>
        </div>
      )}

      {/* Input */}
      <div style={{
        padding: '1rem',
        borderTop: '1px solid #dee2e6',
        backgroundColor: 'white'
      }}>
        <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'center' }}>
          <input
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
            placeholder="Tapez votre message..."
            style={{
              flex: 1,
              padding: '0.75rem 1rem',
              border: '1px solid #dee2e6',
              borderRadius: '25px',
              fontSize: '0.9rem',
              outline: 'none',
              backgroundColor: '#f8f9fa'
            }}
            disabled={isTyping}
          />
          <button
            onClick={handleSendMessage}
            disabled={!inputValue.trim() || isTyping}
            style={{
              width: '40px',
              height: '40px',
              backgroundColor: '#003366',
              color: 'white',
              border: 'none',
              borderRadius: '50%',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '16px',
              opacity: (!inputValue.trim() || isTyping) ? 0.5 : 1
            }}
          >
            ➤
          </button>
        </div>
      </div>

      <style>{`
        @keyframes typing {
          0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
          40% { transform: scale(1); opacity: 1; }
        }
      `}</style>
    </div>
  );
};

export default SimpleChatbot;
