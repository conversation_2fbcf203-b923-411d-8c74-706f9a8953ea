import React from 'react';
import { useChatbot } from '../contexts/ChatbotContext';
import { useLanguage } from '../contexts/LanguageContext';

const QuickActions = () => {
  const { getQuickActions, handleQuickAction } = useChatbot();
  const { isRTL } = useLanguage();
  const quickActions = getQuickActions();

  return (
    <div className="quick-actions">
      <div className="quick-actions-grid">
        {quickActions.map((action, index) => (
          <button
            key={index}
            onClick={() => handleQuickAction(action.action)}
            className="quick-action-btn"
          >
            {action.text}
          </button>
        ))}
      </div>

      <style>{`
        .quick-actions {
          padding: 1rem;
          border-top: 1px solid var(--border-color);
          background: var(--light-gray);
        }

        .quick-actions-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 0.5rem;
        }

        .quick-action-btn {
          padding: 0.5rem 0.75rem;
          background: var(--white);
          border: 1px solid var(--border-color);
          border-radius: 20px;
          font-size: 0.8rem;
          color: var(--primary-blue);
          cursor: pointer;
          transition: var(--transition);
          text-align: center;
        }

        .quick-action-btn:hover {
          background: var(--primary-blue);
          color: var(--white);
          transform: translateY(-1px);
        }

        .quick-action-btn:active {
          transform: translateY(0);
        }
      `}</style>
    </div>
  );
};

export default QuickActions;
