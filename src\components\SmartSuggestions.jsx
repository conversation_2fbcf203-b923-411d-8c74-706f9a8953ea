import React from 'react';
import { useChatbot } from '../contexts/ChatbotContext';
import { useLanguage } from '../contexts/LanguageContext';

const SmartSuggestions = () => {
  const { getSmartSuggestions, sendMessage, userContext } = useChatbot();
  const { isRTL } = useLanguage();
  const suggestions = getSmartSuggestions();

  const handleSuggestionClick = (suggestion) => {
    sendMessage(`Je m'intéresse aux ${suggestion.toLowerCase()}`);
  };

  if (userContext.previousQuestions.length === 0) {
    return null; // Don't show suggestions at the very beginning
  }

  return (
    <div className="smart-suggestions">
      <div className="suggestions-header">
        <span>💡 Suggestions pour vous :</span>
      </div>
      <div className="suggestions-grid">
        {suggestions.map((suggestion, index) => (
          <button
            key={index}
            onClick={() => handleSuggestionClick(suggestion)}
            className="suggestion-chip"
          >
            {suggestion}
          </button>
        ))}
      </div>

      <style>{`
        .smart-suggestions {
          padding: 1rem;
          border-top: 1px solid var(--border-color);
          background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .suggestions-header {
          font-size: 0.85rem;
          color: var(--medium-gray);
          margin-bottom: 0.75rem;
          font-weight: 500;
        }

        .suggestions-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 0.5rem;
        }

        .suggestion-chip {
          padding: 0.5rem 0.75rem;
          background: linear-gradient(135deg, #FFD700 0%, #FFC107 100%);
          color: #003366;
          border: none;
          border-radius: 20px;
          font-size: 0.8rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s ease;
          text-align: center;
          box-shadow: 0 2px 8px rgba(255, 215, 0, 0.3);
        }

        .suggestion-chip:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(255, 215, 0, 0.4);
          background: linear-gradient(135deg, #FFC107 0%, #FF9800 100%);
        }

        .suggestion-chip:active {
          transform: translateY(0);
        }

        @media (max-width: 480px) {
          .suggestions-grid {
            grid-template-columns: 1fr;
          }
        }
      `}</style>
    </div>
  );
};

export default SmartSuggestions;
