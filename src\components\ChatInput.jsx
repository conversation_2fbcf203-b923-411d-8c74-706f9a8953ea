import React, { useState } from 'react';
import { Send } from 'lucide-react';
import { useChatbot } from '../contexts/ChatbotContext';
import { useLanguage } from '../contexts/LanguageContext';

const ChatInput = () => {
  const [inputValue, setInputValue] = useState('');
  const { sendMessage, isTyping } = useChatbot();
  const { t, isRTL } = useLanguage();

  const handleSubmit = (e) => {
    e.preventDefault();
    if (inputValue.trim() && !isTyping) {
      sendMessage(inputValue.trim());
      setInputValue('');
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="chat-input-form">
      <div className="chat-input-container">
        <input
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder={t('chatbot.placeholder')}
          className="chat-input"
          disabled={isTyping}
          dir={isRTL ? 'rtl' : 'ltr'}
        />
        <button
          type="submit"
          className="chat-send-btn"
          disabled={!inputValue.trim() || isTyping}
          title={t('chatbot.send')}
        >
          <Send size={16} />
        </button>
      </div>

      <style>{`
        .chat-input-form {
          padding: 1rem;
          border-top: 1px solid var(--border-color);
          background: var(--white);
        }

        .chat-input-container {
          display: flex;
          gap: 0.5rem;
          align-items: center;
        }

        .chat-input {
          flex: 1;
          padding: 0.75rem 1rem;
          border: 1px solid var(--border-color);
          border-radius: 25px;
          font-size: 0.9rem;
          outline: none;
          transition: var(--transition);
          background: var(--light-gray);
        }

        .chat-input:focus {
          border-color: var(--primary-blue);
          background: var(--white);
        }

        .chat-input:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .chat-send-btn {
          width: 40px;
          height: 40px;
          background: var(--primary-blue);
          color: var(--white);
          border: none;
          border-radius: 50%;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: var(--transition);
        }

        .chat-send-btn:hover:not(:disabled) {
          background: #004080;
          transform: scale(1.05);
        }

        .chat-send-btn:disabled {
          opacity: 0.5;
          cursor: not-allowed;
          transform: none;
        }

        .chat-send-btn:active:not(:disabled) {
          transform: scale(0.95);
        }

        /* RTL Support */
        [dir="rtl"] .chat-input-container {
          flex-direction: row-reverse;
        }
      `}</style>
    </form>
  );
};

export default ChatInput;
