// Data Synchronization Script for Frontend and Backend Stamps
// ===========================================================

// Frontend stamps data (currently hardcoded in Home.jsx)
export const frontendStamps = [
  {
    id: 1,
    nom: "Patrimoine Architectural Tunisien",
    description: "Collection mettant en valeur l'architecture traditionnelle tunisienne",
    prix: 15.50,
    prixOriginal: 18.00,
    categorie: "Patrimoine Tunisien",
    nouveau: true,
    limite: false,
    note: 5,
    enStock: true,
    image: null
  },
  {
    id: 2,
    nom: "Faune Marine de Tunisie",
    description: "Découvrez la richesse marine des côtes tunisiennes",
    prix: 12.00,
    categorie: "Nature & Paysages",
    nouveau: false,
    limite: true,
    note: 4,
    enStock: true,
    image: null
  },
  {
    id: 3,
    nom: "Artistes Tunisiens Contemporains",
    description: "Hommage aux grands artistes tunisiens modernes",
    prix: 20.00,
    categorie: "Art Contemporain",
    nouveau: true,
    limite: true,
    note: 5,
    enStock: true,
    image: null
  },
  {
    id: 4,
    nom: "Habib Bourguiba - Père de l'Indépendance",
    description: "Série commémorative dédiée au premier président tunisien",
    prix: 25.00,
    categorie: "Personnalités",
    nouveau: false,
    limite: false,
    note: 5,
    enStock: true,
    image: null
  },
  {
    id: 5,
    nom: "Jeux Olympiques Paris 2024",
    description: "Participation tunisienne aux Jeux Olympiques",
    prix: 18.50,
    categorie: "Sports",
    nouveau: true,
    limite: false,
    note: 4,
    enStock: false,
    image: null
  },
  {
    id: 6,
    nom: "Révolution du Jasmin",
    description: "Commémoration de la révolution tunisienne de 2011",
    prix: 22.00,
    categorie: "Événements Spéciaux",
    nouveau: false,
    limite: true,
    note: 5,
    enStock: true,
    image: null
  },
  {
    id: 7,
    nom: "Poterie de Nabeul",
    description: "Art traditionnel de la poterie tunisienne de Nabeul",
    prix: 14.00,
    categorie: "Artisanat Traditionnel",
    nouveau: false,
    limite: false,
    note: 4,
    enStock: true,
    image: null
  },
  {
    id: 8,
    nom: "Carthage Antique",
    description: "Sites archéologiques de l'ancienne Carthage",
    prix: 16.50,
    categorie: "Patrimoine Historique",
    nouveau: true,
    limite: false,
    note: 5,
    enStock: true,
    image: null
  },
  {
    id: 9,
    nom: "Oasis du Sud Tunisien",
    description: "Paysages magnifiques des oasis du sud de la Tunisie",
    prix: 13.50,
    categorie: "Nature & Paysages",
    nouveau: false,
    limite: false,
    note: 4,
    enStock: true,
    image: null
  },
  {
    id: 10,
    nom: "Festival de Carthage",
    description: "Célébration du festival international de Carthage",
    prix: 19.00,
    categorie: "Culture & Festivals",
    nouveau: true,
    limite: true,
    note: 5,
    enStock: true,
    image: null
  },
  {
    id: 11,
    nom: "Médina de Tunis",
    description: "Architecture et patrimoine de la médina historique",
    prix: 17.00,
    categorie: "Patrimoine UNESCO",
    nouveau: false,
    limite: false,
    note: 5,
    enStock: true,
    image: null
  },
  {
    id: 12,
    nom: "Couscous Tunisien",
    description: "Patrimoine culinaire tunisien reconnu par l'UNESCO",
    prix: 11.50,
    categorie: "Gastronomie",
    nouveau: true,
    limite: false,
    note: 4,
    enStock: true,
    image: null
  },
  {
    id: 13,
    nom: "Djerba l'Île des Rêves",
    description: "Beauté et traditions de l'île de Djerba",
    prix: 15.00,
    categorie: "Tourisme",
    nouveau: false,
    limite: true,
    note: 4,
    enStock: true,
    image: null
  },
  {
    id: 14,
    nom: "Jasmin de Tunisie",
    description: "Fleur nationale et symbole de la Tunisie",
    prix: 12.50,
    categorie: "Flore Nationale",
    nouveau: true,
    limite: false,
    note: 4,
    enStock: true,
    image: null
  },
  {
    id: 15,
    nom: "Indépendance de la Tunisie",
    description: "Commémoration de l'indépendance tunisienne de 1956",
    prix: 24.00,
    categorie: "Histoire Nationale",
    nouveau: false,
    limite: true,
    note: 5,
    enStock: true,
    image: null
  }
];

// Function to convert frontend format to Strapi format
export const convertToStrapiFormat = (frontendStamp) => {
  return {
    data: {
      nom: frontendStamp.nom,
      description: [
        {
          type: "paragraph",
          children: [
            {
              type: "text",
              text: frontendStamp.description
            }
          ]
        }
      ],
      prix: frontendStamp.prix,
      prixOriginal: frontendStamp.prixOriginal || null,
      categorie: frontendStamp.categorie,
      nouveau: frontendStamp.nouveau || false,
      limite: frontendStamp.limite || false,
      note: frontendStamp.note || 5,
      enStock: frontendStamp.enStock !== false,
      publishedAt: new Date().toISOString()
    }
  };
};

// Function to add stamps to Strapi
export const addStampsToStrapi = async (stamps) => {
  const results = [];
  
  for (const stamp of stamps) {
    try {
      const strapiData = convertToStrapiFormat(stamp);
      
      const response = await fetch('http://localhost:1337/api/timbres', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(strapiData)
      });
      
      if (response.ok) {
        const result = await response.json();
        results.push({ success: true, stamp: stamp.nom, id: result.data.id });
        console.log(`✅ Added: ${stamp.nom}`);
      } else {
        const error = await response.text();
        results.push({ success: false, stamp: stamp.nom, error });
        console.error(`❌ Failed to add ${stamp.nom}:`, error);
      }
    } catch (error) {
      results.push({ success: false, stamp: stamp.nom, error: error.message });
      console.error(`❌ Error adding ${stamp.nom}:`, error);
    }
  }
  
  return results;
};

// Function to get all stamps from Strapi
export const getStampsFromStrapi = async () => {
  try {
    const response = await fetch('http://localhost:1337/api/timbres?populate=*');
    if (response.ok) {
      const data = await response.json();
      return data.data;
    } else {
      console.error('Failed to fetch stamps from Strapi');
      return [];
    }
  } catch (error) {
    console.error('Error fetching stamps:', error);
    return [];
  }
};

// Function to sync all data
export const syncAllStamps = async () => {
  console.log('🔄 Starting stamp synchronization...');
  
  // Get current Strapi stamps
  const strapiStamps = await getStampsFromStrapi();
  console.log(`📋 Found ${strapiStamps.length} stamps in Strapi`);
  
  // Find stamps that exist in frontend but not in Strapi
  const stampsToAdd = frontendStamps.filter(frontendStamp => 
    !strapiStamps.some(strapiStamp => 
      strapiStamp.attributes?.nom === frontendStamp.nom
    )
  );
  
  console.log(`➕ Need to add ${stampsToAdd.length} stamps to Strapi`);
  
  if (stampsToAdd.length > 0) {
    const results = await addStampsToStrapi(stampsToAdd);
    const successful = results.filter(r => r.success).length;
    console.log(`✅ Successfully added ${successful}/${stampsToAdd.length} stamps`);
  }
  
  console.log('🎉 Synchronization complete!');
  return await getStampsFromStrapi();
};
