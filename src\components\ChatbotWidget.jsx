import React from 'react';
import { MessageCircle, X, Minus } from 'lucide-react';
import { useChatbot } from '../contexts/ChatbotContext';
import { useLanguage } from '../contexts/LanguageContext';
import ChatbotWindow from './ChatbotWindow';

const ChatbotWidget = () => {
  const { isOpen, toggleChatbot, closeChatbot } = useChatbot();
  const { t, isRTL } = useLanguage();

  console.log('ChatbotWidget rendered, isOpen:', isOpen); // Debug log

  return (
    <>
      {/* Chatbot Toggle Button */}
      {!isOpen && (
        <button
          onClick={toggleChatbot}
          className="chatbot-toggle"
          aria-label={t('chatbot.title')}
          style={{
            position: 'fixed',
            bottom: '20px',
            right: isRTL ? 'auto' : '20px',
            left: isRTL ? '20px' : 'auto',
            width: '70px',
            height: '70px',
            backgroundColor: '#FFD700', // Tunisian Post Gold
            color: '#003366', // Tunisian Post Blue
            border: '3px solid #003366',
            borderRadius: '50%',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            boxShadow: '0 8px 30px rgba(255, 215, 0, 0.6)',
            zIndex: 99999,
            transition: 'all 0.3s ease',
            animation: 'pulse 2s infinite'
          }}
        >
          <MessageCircle size={28} />
        </button>
      )}

      {/* Alternative transparent button */}
      <button
        onClick={toggleChatbot}
        style={{
          position: 'fixed',
          bottom: '100px',
          right: '20px',
          backgroundColor: 'rgba(0, 51, 102, 0.1)',
          color: '#003366',
          padding: '12px 20px',
          border: '2px solid rgba(0, 51, 102, 0.3)',
          borderRadius: '25px',
          cursor: 'pointer',
          zIndex: 100000,
          fontSize: '14px',
          fontWeight: '500',
          backdropFilter: 'blur(10px)',
          transition: 'all 0.3s ease'
        }}
        onMouseEnter={(e) => {
          e.target.style.backgroundColor = 'rgba(255, 215, 0, 0.2)';
          e.target.style.borderColor = '#FFD700';
        }}
        onMouseLeave={(e) => {
          e.target.style.backgroundColor = 'rgba(0, 51, 102, 0.1)';
          e.target.style.borderColor = 'rgba(0, 51, 102, 0.3)';
        }}
      >
        {isOpen ? '✕ Fermer' : '💬 Chat'}
      </button>

      {/* Chatbot Window */}
      {isOpen && <ChatbotWindow />}

      <style>{`
        @keyframes pulse {
          0% {
            box-shadow: 0 8px 30px rgba(255, 215, 0, 0.6);
          }
          50% {
            box-shadow: 0 8px 30px rgba(255, 215, 0, 0.9), 0 0 0 10px rgba(255, 215, 0, 0.3);
          }
          100% {
            box-shadow: 0 8px 30px rgba(255, 215, 0, 0.6);
          }
        }

        .chatbot-toggle {
          position: fixed !important;
          bottom: 20px !important;
          ${isRTL ? 'left: 20px !important;' : 'right: 20px !important;'}
          width: 70px !important;
          height: 70px !important;
          background: #FFD700 !important;
          color: #003366 !important;
          border: 3px solid #003366 !important;
          border-radius: 50% !important;
          cursor: pointer !important;
          display: flex !important;
          align-items: center !important;
          justify-content: center !important;
          z-index: 99999 !important;
          transition: all 0.3s ease !important;
          animation: pulse 2s infinite !important;
        }

        .chatbot-toggle:hover {
          background: #E6C200 !important;
          transform: scale(1.15) !important;
          box-shadow: 0 12px 40px rgba(255, 215, 0, 0.8) !important;
        }

        .chatbot-toggle:active {
          transform: scale(1.05) !important;
        }

        @media (max-width: 768px) {
          .chatbot-toggle {
            bottom: 15px !important;
            ${isRTL ? 'left: 15px !important;' : 'right: 15px !important;'}
            width: 60px !important;
            height: 60px !important;
          }
        }


      `}</style>
    </>
  );
};

export default ChatbotWidget;
