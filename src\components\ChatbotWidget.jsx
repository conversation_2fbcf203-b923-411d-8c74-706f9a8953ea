import React from 'react';
import { MessageCircle, X, Minus } from 'lucide-react';
import { useChatbot } from '../contexts/ChatbotContext';
import { useLanguage } from '../contexts/LanguageContext';
import ChatbotWindow from './ChatbotWindow';

const ChatbotWidget = () => {
  const { isOpen, toggleChatbot, closeChatbot } = useChatbot();
  const { t, isRTL } = useLanguage();

  console.log('ChatbotWidget rendered, isOpen:', isOpen); // Debug log

  return (
    <>
      {/* Chatbot Toggle Button */}
      {!isOpen && (
        <button
          onClick={toggleChatbot}
          className="chatbot-toggle"
          aria-label={t('chatbot.title')}
          style={{
            position: 'fixed',
            bottom: '20px',
            right: isRTL ? 'auto' : '20px',
            left: isRTL ? '20px' : 'auto',
            width: '60px',
            height: '60px',
            backgroundColor: '#003366',
            color: '#FFFFFF',
            border: 'none',
            borderRadius: '50%',
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            boxShadow: '0 4px 20px rgba(0, 51, 102, 0.3)',
            zIndex: 9999
          }}
        >
          <MessageCircle size={24} />
        </button>
      )}

      {/* Chatbot Window */}
      {isOpen && <ChatbotWindow />}

      <style>{`
        .chatbot-toggle {
          position: fixed;
          bottom: 20px;
          ${isRTL ? 'left: 20px;' : 'right: 20px;'}
          width: 60px;
          height: 60px;
          background: #003366;
          color: #FFFFFF;
          border: none;
          border-radius: 50%;
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: center;
          box-shadow: 0 4px 20px rgba(0, 51, 102, 0.3);
          transition: all 0.3s ease;
          z-index: 9999;
        }

        .chatbot-toggle:hover {
          background: #004080;
          transform: scale(1.1);
          box-shadow: 0 6px 25px rgba(0, 51, 102, 0.4);
        }

        .chatbot-toggle:active {
          transform: scale(0.95);
        }

        @media (max-width: 768px) {
          .chatbot-toggle {
            bottom: 15px;
            ${isRTL ? 'left: 15px;' : 'right: 15px;'}
            width: 50px;
            height: 50px;
          }
        }
      `}</style>
    </>
  );
};

export default ChatbotWidget;
