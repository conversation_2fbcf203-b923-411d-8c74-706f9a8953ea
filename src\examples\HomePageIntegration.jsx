// Example of how to integrate StaticChatbot into your HomePage component

import React, { useEffect, useState } from "react";
import axios from "axios";
import { useLanguage } from "../contexts/LanguageContext";
import StaticChatbot from "../components/StaticChatbot";
import { useChatbotIntegration } from "../hooks/useChatbotIntegration";
import Hero from "../components/Hero";
import CategorySection from "../components/CategorySection";
// ... other imports

function HomePage() {
  const { t } = useLanguage();
  const [timbres, setTimbres] = useState([]);
  const [loading, setLoading] = useState(true);
  
  // Chatbot integration
  const { handleScrollToStamp, addStampIds } = useChatbotIntegration(timbres);

  useEffect(() => {
    const fetchTimbres = async () => {
      try {
        setLoading(true);
        const response = await axios.get("http://localhost:1337/api/timbres?populate=*");
        setTimbres(response.data.data);
      } catch (err) {
        console.error("Error fetching stamps:", err);
      } finally {
        setLoading(false);
      }
    };

    fetchTimbres();
  }, []);

  // Add IDs to stamp cards after they're rendered
  useEffect(() => {
    if (!loading && timbres.length > 0) {
      addStampIds();
    }
  }, [loading, timbres, addStampIds]);

  return (
    <div className="homepage">
      <Hero />
      
      <CategorySection />
      
      {/* Featured Stamps Section */}
      <section className="featured-stamps" id="timbres-section">
        <div className="container">
          <h2>{t('stamps.title')}</h2>
          <div className="stamps-grid">
            {timbres.slice(0, 6).map((timbre) => (
              <div key={timbre.id} className="stamp-card">
                {/* Your stamp card content */}
                <h3>{timbre.attributes?.title}</h3>
                <p>{timbre.attributes?.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Static Chatbot - This will appear on all pages where you include it */}
      <StaticChatbot 
        timbres={timbres} 
        onScrollToStamp={handleScrollToStamp}
      />

      <style>{`
        /* Add the highlight effect CSS */
        .chatbot-highlighted {
          animation: chatbot-highlight 2s ease-in-out;
          transform: scale(1.05);
          box-shadow: 0 0 20px rgba(255, 215, 0, 0.8) !important;
          z-index: 10;
          position: relative;
        }

        @keyframes chatbot-highlight {
          0% { 
            box-shadow: 0 0 0 rgba(255, 215, 0, 0.8);
            transform: scale(1);
          }
          50% { 
            box-shadow: 0 0 30px rgba(255, 215, 0, 1);
            transform: scale(1.08);
          }
          100% { 
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.8);
            transform: scale(1.05);
          }
        }
      `}</style>
    </div>
  );
}

export default HomePage;

/*
INTEGRATION INSTRUCTIONS:

1. Import the required components and hooks:
   - StaticChatbot from "../components/StaticChatbot"
   - useChatbotIntegration from "../hooks/useChatbotIntegration"

2. Use the chatbot integration hook:
   const { handleScrollToStamp, addStampIds } = useChatbotIntegration(timbres);

3. Add the StaticChatbot component before the closing div:
   <StaticChatbot timbres={timbres} onScrollToStamp={handleScrollToStamp} />

4. Make sure to call addStampIds() after your stamps are rendered:
   useEffect(() => {
     if (!loading && timbres.length > 0) {
       addStampIds();
     }
   }, [loading, timbres, addStampIds]);

5. Add an id="timbres-section" to your stamps container for "show all" functionality

6. Add the highlight CSS animation to your component styles

CHATBOT FEATURES:
- Searches for stamps by name/keyword
- Scrolls to specific stamps and highlights them
- Shows all stamps when requested
- Provides site information
- Contact information
- Fully responsive design
- Tunisia Post color scheme (gold/blue)
- Minimize/maximize functionality
- Mobile-friendly full-screen mode

EXAMPLE INTERACTIONS:
- "Je veux voir le timbre Patrimoine Tunisien"
- "Montrez-moi tous les timbres"
- "Qu'est-ce que ce site ?"
- "Informations de contact"
- "Bonjour"
*/
