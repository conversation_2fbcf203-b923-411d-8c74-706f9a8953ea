import React, { createContext, useContext, useState, useEffect } from 'react';
import { useLanguage } from './LanguageContext';

const ChatbotContext = createContext();

export const useChatbot = () => {
  const context = useContext(ChatbotContext);
  if (!context) {
    throw new Error('useChatbot must be used within a ChatbotProvider');
  }
  return context;
};

export const ChatbotProvider = ({ children }) => {
  const { t } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState([]);
  const [isTyping, setIsTyping] = useState(false);
  const [conversationStarted, setConversationStarted] = useState(false);

  console.log('ChatbotProvider initialized, isOpen:', isOpen); // Debug log

  // Initialize chatbot with welcome message
  useEffect(() => {
    if (!conversationStarted) {
      const welcomeMessage = {
        id: Date.now(),
        text: t('chatbot.welcome'),
        sender: 'bot',
        timestamp: new Date()
      };
      setMessages([welcomeMessage]);
      setConversationStarted(true);
    }
  }, [t, conversationStarted]);

  const openChatbot = () => {
    setIsOpen(true);
  };

  const closeChatbot = () => {
    setIsOpen(false);
  };

  const toggleChatbot = () => {
    setIsOpen(!isOpen);
  };

  const addMessage = (text, sender = 'user') => {
    const message = {
      id: Date.now() + Math.random(),
      text,
      sender,
      timestamp: new Date()
    };
    setMessages(prev => [...prev, message]);
    return message;
  };

  const sendMessage = async (text) => {
    // Add user message
    addMessage(text, 'user');

    // Show typing indicator
    setIsTyping(true);

    // Simulate bot response delay
    setTimeout(() => {
      const botResponse = generateBotResponse(text);
      addMessage(botResponse, 'bot');
      setIsTyping(false);

      // Play notification sound (visual feedback only)
      if (!isOpen) {
        // If chatbot is closed, this would trigger a notification
        console.log('New message received');
      }
    }, 1000 + Math.random() * 1000); // Random delay between 1-2 seconds
  };

  const generateBotResponse = (userMessage) => {
    const message = userMessage.toLowerCase();

    // Simple keyword-based responses
    if (message.includes('bonjour') || message.includes('salut') || message.includes('hello') || message.includes('hi')) {
      return t('chatbot.responses.greeting');
    }

    if (message.includes('timbre') || message.includes('stamp') || message.includes('طابع')) {
      return t('chatbot.responses.stamps');
    }

    if (message.includes('prix') || message.includes('price') || message.includes('cost') || message.includes('سعر')) {
      return t('chatbot.responses.pricing');
    }

    if (message.includes('commande') || message.includes('order') || message.includes('طلب')) {
      return t('chatbot.responses.orders');
    }

    if (message.includes('livraison') || message.includes('delivery') || message.includes('shipping') || message.includes('توصيل')) {
      return t('chatbot.responses.delivery');
    }

    if (message.includes('contact') || message.includes('aide') || message.includes('help') || message.includes('مساعدة')) {
      return t('chatbot.responses.contact');
    }

    if (message.includes('merci') || message.includes('thank') || message.includes('شكرا')) {
      return t('chatbot.responses.thanks');
    }

    if (message.includes('au revoir') || message.includes('bye') || message.includes('goodbye') || message.includes('وداعا')) {
      return t('chatbot.responses.goodbye');
    }

    // Additional specific responses
    if (message.includes('patrimoine') || message.includes('heritage') || message.includes('تراث')) {
      return "Notre collection patrimoine comprend des timbres célébrant l'histoire et la culture tunisiennes. Vous y trouverez des monuments historiques, des sites archéologiques et des traditions ancestrales.";
    }

    if (message.includes('nature') || message.includes('paysage') || message.includes('landscape') || message.includes('طبيعة')) {
      return "Nos timbres nature mettent en valeur la beauté des paysages tunisiens : déserts, oasis, côtes méditerranéennes, et la faune locale unique.";
    }

    if (message.includes('sport') || message.includes('football') || message.includes('رياضة')) {
      return "Découvrez nos timbres sportifs commémorant les grands événements sportifs tunisiens et internationaux, ainsi que nos champions nationaux.";
    }

    if (message.includes('nouveau') || message.includes('new') || message.includes('récent') || message.includes('جديد')) {
      return "Nos dernières nouveautés sont régulièrement ajoutées dans la section 'Nouveautés'. Je vous recommande de consulter cette page pour découvrir nos derniers timbres !";
    }

    // Default response
    return t('chatbot.responses.default');
  };

  const getQuickActions = () => {
    return [
      { text: t('chatbot.quickActions.viewStamps'), action: 'stamps' },
      { text: t('chatbot.quickActions.pricing'), action: 'pricing' },
      { text: t('chatbot.quickActions.delivery'), action: 'delivery' },
      { text: t('chatbot.quickActions.contact'), action: 'contact' }
    ];
  };

  const handleQuickAction = (action) => {
    let response = '';
    switch (action) {
      case 'stamps':
        response = t('chatbot.responses.stamps');
        break;
      case 'pricing':
        response = t('chatbot.responses.pricing');
        break;
      case 'delivery':
        response = t('chatbot.responses.delivery');
        break;
      case 'contact':
        response = t('chatbot.responses.contact');
        break;
      default:
        response = t('chatbot.responses.default');
    }

    addMessage(response, 'bot');
  };

  const clearMessages = () => {
    setMessages([]);
    setConversationStarted(false);
  };

  const value = {
    isOpen,
    messages,
    isTyping,
    openChatbot,
    closeChatbot,
    toggleChatbot,
    sendMessage,
    clearMessages,
    addMessage,
    getQuickActions,
    handleQuickAction
  };

  return (
    <ChatbotContext.Provider value={value}>
      {children}
    </ChatbotContext.Provider>
  );
};
