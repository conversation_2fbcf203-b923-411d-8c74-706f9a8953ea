import React, { createContext, useContext, useState, useEffect } from 'react';
import { useLanguage } from './LanguageContext';

const ChatbotContext = createContext();

export const useChatbot = () => {
  const context = useContext(ChatbotContext);
  if (!context) {
    throw new Error('useChatbot must be used within a ChatbotProvider');
  }
  return context;
};

export const ChatbotProvider = ({ children }) => {
  const { t } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState([]);
  const [isTyping, setIsTyping] = useState(false);
  const [conversationStarted, setConversationStarted] = useState(false);
  const [userContext, setUserContext] = useState({
    name: null,
    interests: [],
    previousQuestions: [],
    language: 'fr',
    sessionStartTime: new Date()
  });

  // Initialize chatbot with welcome message
  useEffect(() => {
    if (!conversationStarted) {
      const welcomeMessage = {
        id: Date.now(),
        text: t('chatbot.welcome'),
        sender: 'bot',
        timestamp: new Date()
      };
      setMessages([welcomeMessage]);
      setConversationStarted(true);
    }
  }, [t, conversationStarted]);

  const openChatbot = () => {
    setIsOpen(true);
  };

  const closeChatbot = () => {
    setIsOpen(false);
  };

  const toggleChatbot = () => {
    setIsOpen(!isOpen);
  };

  const addMessage = (text, sender = 'user') => {
    const message = {
      id: Date.now() + Math.random(),
      text,
      sender,
      timestamp: new Date()
    };
    setMessages(prev => [...prev, message]);
    return message;
  };

  const sendMessage = async (text) => {
    // Add user message
    addMessage(text, 'user');

    // Update user context
    updateUserContext(text);

    // Show typing indicator
    setIsTyping(true);

    // Simulate bot response delay with more realistic timing
    const responseDelay = text.length > 50 ? 2000 : 1500; // Longer delay for longer messages

    setTimeout(() => {
      const botResponse = generateIntelligentResponse(text);
      addMessage(botResponse, 'bot');
      setIsTyping(false);

      // Suggest follow-up questions occasionally
      if (Math.random() > 0.7) {
        setTimeout(() => {
          const followUp = generateFollowUpSuggestion();
          if (followUp) {
            addMessage(followUp, 'bot');
          }
        }, 2000);
      }
    }, responseDelay + Math.random() * 500);
  };

  const updateUserContext = (message) => {
    const lowerMessage = message.toLowerCase();

    // Extract user name if mentioned
    const nameMatch = message.match(/je m'appelle (\w+)|my name is (\w+)|اسمي (\w+)/i);
    if (nameMatch) {
      const name = nameMatch[1] || nameMatch[2] || nameMatch[3];
      setUserContext(prev => ({ ...prev, name }));
    }

    // Track interests based on keywords
    const interests = [];
    if (lowerMessage.includes('patrimoine') || lowerMessage.includes('heritage') || lowerMessage.includes('تراث')) {
      interests.push('heritage');
    }
    if (lowerMessage.includes('nature') || lowerMessage.includes('paysage') || lowerMessage.includes('طبيعة')) {
      interests.push('nature');
    }
    if (lowerMessage.includes('sport') || lowerMessage.includes('رياضة')) {
      interests.push('sports');
    }
    if (lowerMessage.includes('histoire') || lowerMessage.includes('history') || lowerMessage.includes('تاريخ')) {
      interests.push('history');
    }

    // Update context
    setUserContext(prev => ({
      ...prev,
      interests: [...new Set([...prev.interests, ...interests])],
      previousQuestions: [...prev.previousQuestions, lowerMessage].slice(-5) // Keep last 5 questions
    }));
  };

  const generateIntelligentResponse = (userMessage) => {
    const message = userMessage.toLowerCase();
    const userName = userContext.name;
    const userInterests = userContext.interests;

    // Personalized greeting
    if (message.includes('bonjour') || message.includes('salut') || message.includes('hello') || message.includes('hi')) {
      if (userName) {
        return `Bonjour ${userName} ! Ravi de vous revoir. Comment puis-je vous aider aujourd'hui avec notre collection de timbres ?`;
      }
      return t('chatbot.responses.greeting');
    }

    // Intelligent stamp responses based on context
    if (message.includes('timbre') || message.includes('stamp') || message.includes('طابع')) {
      if (userInterests.length > 0) {
        const interest = userInterests[0];
        return `Parfait ! Je vois que vous vous intéressez au ${interest}. Nous avons une excellente collection de timbres dans cette catégorie. Souhaitez-vous que je vous présente nos pièces les plus remarquables ?`;
      }
      return t('chatbot.responses.stamps');
    }

    // Advanced contextual responses
    if (message.includes('recommand') || message.includes('suggest') || message.includes('conseil')) {
      return generatePersonalizedRecommendation();
    }

    // Historical and educational responses
    if (message.includes('histoire') || message.includes('history') || message.includes('quand') || message.includes('when')) {
      return generateHistoricalResponse(message);
    }

    // Technical stamp information
    if (message.includes('technique') || message.includes('impression') || message.includes('papier') || message.includes('printing')) {
      return "Les timbres tunisiens utilisent diverses techniques d'impression : offset, taille-douce, et héliogravure. Le papier utilisé est généralement du papier couché de haute qualité avec des filigranes de sécurité. Souhaitez-vous en savoir plus sur une technique particulière ?";
    }

    // Collection and rarity information
    if (message.includes('rare') || message.includes('collection') || message.includes('valeur') || message.includes('value')) {
      return "La valeur des timbres dépend de plusieurs facteurs : rareté, état de conservation, erreurs d'impression, et demande des collectionneurs. Nos timbres les plus recherchés incluent les premières émissions de l'indépendance et les séries limitées. Avez-vous un timbre particulier en tête ?";
    }

    // Specific stamp series
    if (message.includes('indépendance') || message.includes('independence')) {
      return "Les timbres de l'indépendance tunisienne (1956) sont parmi nos pièces les plus emblématiques. Ils marquent un tournant historique et présentent des symboles nationaux forts. Cette série comprend le portrait du Président Bourguiba et les armoiries nationales.";
    }

    if (message.includes('carthage') || message.includes('قرطاج')) {
      return "Carthage occupe une place spéciale dans notre collection ! Nous avons plusieurs séries dédiées à cette cité antique : les ruines archéologiques, les mosaïques, et les reconstitutions historiques. C'est un témoignage fascinant de notre patrimoine millénaire.";
    }

    // Modern topics
    if (message.includes('digital') || message.includes('numérique') || message.includes('moderne')) {
      return "Bien que nous célébrons l'art traditionnel du timbre, nous explorons aussi les innovations modernes : timbres holographiques, QR codes, et techniques d'impression 3D. L'avenir de la philatélie combine tradition et technologie !";
    }

    // Emotional and personal responses
    if (message.includes('passion') || message.includes('amour') || message.includes('love')) {
      return "Je partage votre passion ! La philatélie est bien plus qu'une collection, c'est un voyage à travers l'histoire, l'art et la culture. Chaque timbre raconte une histoire unique. Qu'est-ce qui vous passionne le plus dans les timbres ?";
    }

    // Default with context awareness
    if (userContext.previousQuestions.length > 0) {
      return "Je vois que vous explorez différents aspects de notre collection. N'hésitez pas à me poser des questions plus spécifiques - je peux vous parler d'histoire, de techniques d'impression, de valeurs, ou vous recommander des timbres selon vos goûts !";
    }

    return t('chatbot.responses.default');
  };

  const generatePersonalizedRecommendation = () => {
    const interests = userContext.interests;

    if (interests.includes('heritage')) {
      return "Basé sur votre intérêt pour le patrimoine, je vous recommande notre série 'Monuments de Tunisie' et les timbres commémoratifs des sites UNESCO. Ils allient beauté artistique et valeur historique.";
    }

    if (interests.includes('nature')) {
      return "Pour un amateur de nature comme vous, je suggère notre collection 'Faune et Flore de Tunisie' et la série 'Parcs Nationaux'. Les couleurs et détails sont exceptionnels !";
    }

    if (interests.includes('sports')) {
      return "En tant que passionné de sport, vous apprécierez nos timbres des Jeux Olympiques, les championnats de football, et nos hommages aux athlètes tunisiens. Une belle façon de célébrer l'excellence sportive !";
    }

    return "Pour commencer votre exploration, je recommande notre sélection 'Incontournables' qui présente les timbres les plus représentatifs de chaque époque et thématique.";
  };

  const generateHistoricalResponse = (message) => {
    if (message.includes('premier') || message.includes('first') || message.includes('début')) {
      return "Le premier timbre tunisien a été émis en 1888 sous le protectorat français. Après l'indépendance en 1956, la Tunisie a développé sa propre identité philatélique avec des designs reflétant sa culture et son histoire.";
    }

    if (message.includes('bourguiba') || message.includes('président')) {
      return "Habib Bourguiba, père de l'indépendance, apparaît sur de nombreux timbres historiques. Ces émissions témoignent des grandes étapes de la construction nationale tunisienne.";
    }

    return "L'histoire philatélique tunisienne est riche de plus d'un siècle. Chaque période a ses caractéristiques : protectorat, indépendance, république... Quelle époque vous intéresse le plus ?";
  };

  const generateFollowUpSuggestion = () => {
    const suggestions = [
      "Souhaitez-vous que je vous présente nos nouveautés ?",
      "Avez-vous des questions sur une période historique particulière ?",
      "Voulez-vous en savoir plus sur les techniques d'impression ?",
      "Puis-je vous recommander des timbres selon vos goûts ?",
      "Aimeriez-vous découvrir nos séries thématiques ?"
    ];

    return suggestions[Math.floor(Math.random() * suggestions.length)];
  };

  const getQuickActions = () => {
    return [
      { text: t('chatbot.quickActions.viewStamps'), action: 'stamps' },
      { text: t('chatbot.quickActions.pricing'), action: 'pricing' },
      { text: t('chatbot.quickActions.delivery'), action: 'delivery' },
      { text: t('chatbot.quickActions.contact'), action: 'contact' }
    ];
  };

  const handleQuickAction = (action) => {
    let response = '';
    switch (action) {
      case 'stamps':
        response = t('chatbot.responses.stamps');
        break;
      case 'pricing':
        response = t('chatbot.responses.pricing');
        break;
      case 'delivery':
        response = t('chatbot.responses.delivery');
        break;
      case 'contact':
        response = t('chatbot.responses.contact');
        break;
      default:
        response = t('chatbot.responses.default');
    }

    addMessage(response, 'bot');
  };

  const clearMessages = () => {
    setMessages([]);
    setConversationStarted(false);
    setUserContext({
      name: null,
      interests: [],
      previousQuestions: [],
      language: 'fr',
      sessionStartTime: new Date()
    });
  };

  const getSmartSuggestions = () => {
    const suggestions = [];

    if (userContext.interests.includes('heritage')) {
      suggestions.push("Monuments historiques", "Sites UNESCO", "Architecture traditionnelle");
    }

    if (userContext.interests.includes('nature')) {
      suggestions.push("Faune tunisienne", "Parcs nationaux", "Paysages du Sahara");
    }

    if (userContext.interests.includes('sports')) {
      suggestions.push("Jeux Olympiques", "Football tunisien", "Champions nationaux");
    }

    if (suggestions.length === 0) {
      return ["Timbres populaires", "Nouveautés 2024", "Collection patrimoine", "Séries limitées"];
    }

    return suggestions.slice(0, 4);
  };

  const getConversationSummary = () => {
    return {
      messageCount: messages.length,
      userInterests: userContext.interests,
      userName: userContext.name,
      sessionDuration: Math.floor((new Date() - userContext.sessionStartTime) / 1000 / 60) // in minutes
    };
  };

  const value = {
    isOpen,
    messages,
    isTyping,
    openChatbot,
    closeChatbot,
    toggleChatbot,
    sendMessage,
    clearMessages,
    addMessage,
    getQuickActions,
    handleQuickAction,
    userContext,
    getSmartSuggestions,
    getConversationSummary
  };

  return (
    <ChatbotContext.Provider value={value}>
      {children}
    </ChatbotContext.Provider>
  );
};
