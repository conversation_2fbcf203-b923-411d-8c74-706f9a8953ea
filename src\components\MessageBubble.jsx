import React from 'react';
import { useLanguage } from '../contexts/LanguageContext';

const MessageBubble = ({ message }) => {
  const { isRTL } = useLanguage();
  const isBot = message.sender === 'bot';
  const isUser = message.sender === 'user';

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  return (
    <div className={`message-bubble ${isBot ? 'bot-message' : 'user-message'}`}>
      <div className="message-content">
        <p>{message.text}</p>
        <span className="message-time">{formatTime(message.timestamp)}</span>
      </div>

      <style>{`
        .message-bubble {
          display: flex;
          margin-bottom: 0.5rem;
        }

        .bot-message {
          justify-content: flex-start;
        }

        .user-message {
          justify-content: flex-end;
        }

        .message-content {
          max-width: 80%;
          padding: 0.75rem 1rem;
          border-radius: 18px;
          position: relative;
          word-wrap: break-word;
        }

        .bot-message .message-content {
          background: var(--light-gray);
          color: var(--dark-gray);
          border-bottom-${isRTL ? 'right' : 'left'}-radius: 4px;
        }

        .user-message .message-content {
          background: var(--primary-blue);
          color: var(--white);
          border-bottom-${isRTL ? 'left' : 'right'}-radius: 4px;
        }

        .message-content p {
          margin: 0;
          font-size: 0.9rem;
          line-height: 1.4;
        }

        .message-time {
          font-size: 0.7rem;
          opacity: 0.7;
          display: block;
          margin-top: 0.25rem;
          text-align: ${isRTL ? 'left' : 'right'};
        }

        .bot-message .message-time {
          text-align: ${isRTL ? 'right' : 'left'};
        }

        /* RTL Support */
        [dir="rtl"] .bot-message .message-content {
          border-bottom-left-radius: 18px;
          border-bottom-right-radius: 4px;
        }

        [dir="rtl"] .user-message .message-content {
          border-bottom-right-radius: 18px;
          border-bottom-left-radius: 4px;
        }

        [dir="rtl"] .message-time {
          text-align: left;
        }

        [dir="rtl"] .bot-message .message-time {
          text-align: right;
        }
      `}</style>
    </div>
  );
};

export default MessageBubble;
